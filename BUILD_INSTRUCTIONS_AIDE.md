# 🛠️ BUILD INSTRUCTIONS FOR AIDE CMOD

## 📋 **REQUIREMENTS**
- AIDE CMOD (Latest version)
- Android device with sufficient storage
- Root access (recommended for testing)

## 🚀 **BUILD STEPS**

### **1. Open Project in AIDE CMOD**
```
1. Launch AIDE CMOD
2. Open existing project
3. Navigate to project folder
4. Select build.gradle (root)
```

### **2. Sync Project**
```
1. Wait for AIDE to sync dependencies
2. Check for any missing dependencies
3. Resolve any sync issues
```

### **3. Build Native Library**
```
1. Go to app/src/main/jni/
2. AIDE will automatically detect Android.mk
3. Build native library (Client)
4. Check for compilation errors
```

### **4. Build APK**
```
1. Select "Build APK" from menu
2. Wait for compilation
3. Check build output for errors
4. Install APK on device
```

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Native Compilation Errors**
```
Error: undefined reference to 'OBFUSCATE'
Solution: Make sure obfuscate.h is included properly
```

#### **2. JNI Method Not Found**
```
Error: java.lang.UnsatisfiedLinkError
Solution: Check method signatures match exactly
```

#### **3. Missing Headers**
```
Error: fatal error: 'GameGuardianDetector.h' file not found
Solution: Ensure header file is in correct location
```

### **Fixes:**

#### **1. Clean and Rebuild**
```
1. Delete app/build/ folder
2. Clean project in AIDE
3. Rebuild from scratch
```

#### **2. Check Android.mk**
```
Ensure GameGuardianDetector.cpp is listed in LOCAL_SRC_FILES
```

#### **3. Verify JNI Signatures**
```
Java: private native static int nativeDetectGameGuardian();
C++: Java_com_hmod_vip_Main_nativeDetectGameGuardian
```

## 🧪 **TESTING**

### **1. Enable Test Mode**
```java
// In MainActivity.onCreate(), uncomment:
GameGuardianTest.testDetection(this);
GameGuardianTest.testNativeMethods();
```

### **2. Check Logs**
```
adb logcat | grep -E "(GGDetector|GGTest|ModLoader)"
```

### **3. Test Scenarios**
```
1. Clean device (no Game Guardian)
2. Device with Game Guardian installed
3. Device with Game Guardian running
4. Rooted device without Game Guardian
```

## 🔄 **ARCHITECTURE CHANGE**

**Game Guardian Detection** đã được chuyển từ **Main.cpp** sang **Client.cpp**:

### **Why Client.cpp?**
- ✅ **Load ngay khi app start** - Không cần đợi tải mod
- ✅ **Always available** - Luôn có sẵn kể cả khi offline
- ✅ **Better security** - Kiểm tra trước khi load bất kỳ mod nào
- ✅ **AIDE Compatible** - Tương thích hoàn hảo với AIDE CMOD

### **Files Structure:**
```
📁 app/src/main/jni/
├── Client.cpp (✅ Contains Game Guardian Detection)
├── GameGuardianDetector.h (Header file)
├── Main.cpp (Server-side mod logic)
└── Android.mk (Updated build script)
```

## 📱 **DEPLOYMENT**

### **Production Build:**
```
1. Comment out test calls in MainActivity
2. Set minifyEnabled = true in build.gradle
3. Enable ProGuard obfuscation
4. Build release APK
5. Sign APK
```

### **Security Checklist:**
```
✅ Test calls removed
✅ Debug logs disabled
✅ Obfuscation enabled
✅ Anti-tampering active
✅ Detection working correctly
```

## 🚨 **IMPORTANT NOTES**

1. **AIDE Compatibility**: Code is optimized for AIDE CMOD
2. **C++17 Standard**: Uses C++17 features compatible with AIDE
3. **No External Dependencies**: All detection code is self-contained
4. **Performance Optimized**: Minimal impact on app startup
5. **False Positive Safe**: Designed to avoid false positives

## 📞 **SUPPORT**

If you encounter build issues:
1. Check AIDE CMOD version (latest recommended)
2. Verify Android NDK is properly configured
3. Ensure sufficient device storage
4. Check logcat for detailed error messages

## ⚡ **QUICK BUILD COMMAND**
```
For AIDE CMOD:
1. Open project
2. Build → Build APK
3. Install and test
```
