apply plugin: 'com.android.application'

android {
    compileSdkVersion 29
    defaultConfig {
        applicationId "com.hmod.vip"
        minSdkVersion 19
        targetSdkVersion 29
        versionCode 1
        versionName "3.2"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86'
        }
        signingConfig signingConfigs.debug
    }
    buildTypes {
        release {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file('src/main/jni/Android.mk')
        }
    }
}

//dependencies must be placed below 'android' brackets to get it work on AIDE
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}
