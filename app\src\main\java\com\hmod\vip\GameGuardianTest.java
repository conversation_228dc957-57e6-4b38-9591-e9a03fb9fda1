package com.hmod.vip;

import android.content.Context;
import android.util.Log;

/**
 * 🛡️ GAME GUARDIAN DETECTION TEST CLASS
 * Dùng để test detection trên AIDE CMOD
 * Detection code đã được chuyển vào Client.cpp để load ngay khi app start
 */
public class GameGuardianTest {
    private static final String TAG = "GGTest";
    
    /**
     * Test Game Guardian detection
     */
    public static void testDetection(Context context) {
        Log.d(TAG, "🧪 Starting Game Guardian detection test...");
        
        try {
            // Test quick detection
            boolean quickResult = Main.GameGuardianDetector.quickDetect();
            Log.d(TAG, "Quick detection result: " + quickResult);
            
            // Test full detection
            Main.GameGuardianDetector.DetectionResult result = 
                Main.GameGuardianDetector.detectGameGuardian();
            
            Log.d(TAG, "=== DETECTION RESULTS ===");
            Log.d(TAG, "Detected: " + result.detected);
            Log.d(TAG, "Confidence: " + result.confidence + "%");
            Log.d(TAG, "Method: " + result.method);
            Log.d(TAG, "Details: " + result.details);
            Log.d(TAG, "========================");
            
            if (result.detected) {
                Log.w(TAG, "⚠️ Game Guardian detected on this device!");
            } else {
                Log.i(TAG, "✅ Device is clean - No Game Guardian detected");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Test error: " + e.getMessage());
        }
    }
    
    /**
     * Test native methods individually
     */
    public static void testNativeMethods() {
        Log.d(TAG, "🧪 Testing native methods...");
        
        try {
            // Test anti-debug setup
            int antiDebugResult = Main.nativeSetupAntiDebug();
            Log.d(TAG, "Anti-debug setup result: " + antiDebugResult);
            
            // Test TracerPid detection
            int tracerResult = Main.nativeDetectGameGuardian();
            Log.d(TAG, "TracerPid detection confidence: " + tracerResult + "%");
            
            // Test memory tampering
            boolean memoryResult = Main.nativeDetectMemoryTampering();
            Log.d(TAG, "Memory tampering detected: " + memoryResult);
            
            // Test suspicious processes
            boolean processResult = Main.nativeDetectSuspiciousProcesses();
            Log.d(TAG, "Suspicious processes detected: " + processResult);
            
        } catch (Exception e) {
            Log.e(TAG, "Native test error: " + e.getMessage());
        }
    }
}
