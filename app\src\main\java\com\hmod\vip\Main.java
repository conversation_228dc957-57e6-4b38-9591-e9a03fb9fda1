package com.hmod.vip;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Handler;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.RotateAnimation;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.GridLayout;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.lang.ref.WeakReference;
import org.json.JSONObject;
import android.os.Build;

public class Main {
    // Các biến static và hằng số
    private static WeakReference<Context> mContextRef;
    private static String LIB_NAME = ""; // Sẽ được set động khi người dùng chọn mod
    private static boolean CURRENT_MOD_VPN_CHECK_ENABLED = false; // Trạng thái VPN check của mod hiện tại (default false)
    private static boolean MOD_SELECTED = false; // Đã chọn mod chưa
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());
    private static TextView progressText;
    private static TextView statusText;
    private static ProgressBar downloadBar;
    private static TextView loadingGearIcon; // Đã đổi từ ImageView sang TextView
    private static int downloadProgress = 0;
    private static AlertDialog currentDialog;
    private static Map<String, ModStatus> modStatuses = new HashMap<>();
    private static boolean isLoading = false;
    private static View globalBlockingOverlay = null;
    private static RotateAnimation rotateAnimation;
    private static boolean animationShouldRun = false;
    private static Handler arrowAnimHandler;
    private static boolean isDialogShowing = false;
    private static final String TAG = "ModLoader";
    private static final int MAX_RETRY_COUNT = 3;
    private static final int MAX_BUFFER_SIZE = 10 * 1024 * 1024; // 10MB max buffer size
    private static final String PREFS_NAME = "ModLoaderPrefs";
    private static final String PREF_DARK_MODE = "darkMode";
    private static boolean isDarkMode = false;

    // Định nghĩa các màu sắc cho chế độ sáng (Light Mode)
    private static final String LIGHT_COLOR_PRIMARY = "#FFFFFF";
    private static final String LIGHT_COLOR_TEXT_PRIMARY = "#000000";
    private static final String LIGHT_COLOR_TEXT_SECONDARY = "#8E8E93";
    private static final String LIGHT_COLOR_GREEN = "#34C759";
    private static final String LIGHT_COLOR_RED = "#FF3B30";
    private static final String LIGHT_COLOR_BLUE = "#007AFF";
    private static final String LIGHT_COLOR_ORANGE = "#FF9500";
    private static final String LIGHT_COLOR_PURPLE = "#AF52DE";
    private static final String LIGHT_COLOR_GREEN_LIGHT = "#E6F9ED";
    private static final String LIGHT_COLOR_RED_LIGHT = "#FFEAEA";
    private static final String LIGHT_COLOR_BLUE_LIGHT = "#E5F2FF";
    private static final String LIGHT_COLOR_ORANGE_LIGHT = "#FFF4E5";
    private static final String LIGHT_COLOR_GRAY_BACKGROUND = "#F2F2F7";
    private static final String LIGHT_COLOR_SEPARATOR = "#C6C6C8";

    // Định nghĩa các màu sắc cho chế độ tối (Dark Mode)
    private static final String DARK_COLOR_PRIMARY = "#1C1C1E";
    private static final String DARK_COLOR_TEXT_PRIMARY = "#FFFFFF";
    private static final String DARK_COLOR_TEXT_SECONDARY = "#AEAEB2";
    private static final String DARK_COLOR_GREEN = "#30D158";
    private static final String DARK_COLOR_RED = "#FF453A";
    private static final String DARK_COLOR_BLUE = "#0A84FF";
    private static final String DARK_COLOR_ORANGE = "#FF9F0A";
    private static final String DARK_COLOR_PURPLE = "#BF5AF2";
    private static final String DARK_COLOR_GREEN_LIGHT = "#173824";
    private static final String DARK_COLOR_RED_LIGHT = "#3B1A1A";
    private static final String DARK_COLOR_BLUE_LIGHT = "#132338";
    private static final String DARK_COLOR_ORANGE_LIGHT = "#3D2911";
    private static final String DARK_COLOR_GRAY_BACKGROUND = "#2C2C2E";
    private static final String DARK_COLOR_SEPARATOR = "#38383A";
    private static final String DARK_COLOR_CARD_BACKGROUND = "#3A3A3C";

    // Phương thức hỗ trợ để lấy màu dựa trên chế độ hiện tại
    private static String getColorPrimary() {
        return isDarkMode ? DARK_COLOR_PRIMARY : LIGHT_COLOR_PRIMARY;
    }

    private static String getColorTextPrimary() {
        return isDarkMode ? DARK_COLOR_TEXT_PRIMARY : LIGHT_COLOR_TEXT_PRIMARY;
    }

    private static String getColorTextSecondary() {
        return isDarkMode ? DARK_COLOR_TEXT_SECONDARY : LIGHT_COLOR_TEXT_SECONDARY;
    }

    private static String getColorGreen() {
        return isDarkMode ? DARK_COLOR_GREEN : LIGHT_COLOR_GREEN;
    }

    private static String getColorRed() {
        return isDarkMode ? DARK_COLOR_RED : LIGHT_COLOR_RED;
    }

    private static String getColorBlue() {
        return isDarkMode ? DARK_COLOR_BLUE : LIGHT_COLOR_BLUE;
    }

    private static String getColorOrange() {
        return isDarkMode ? DARK_COLOR_ORANGE : LIGHT_COLOR_ORANGE;
    }

    private static String getColorPurple() {
        return isDarkMode ? DARK_COLOR_PURPLE : LIGHT_COLOR_PURPLE;
    }

    private static String getColorGreenLight() {
        return isDarkMode ? DARK_COLOR_GREEN_LIGHT : LIGHT_COLOR_GREEN_LIGHT;
    }

    private static String getColorRedLight() {
        return isDarkMode ? DARK_COLOR_RED_LIGHT : LIGHT_COLOR_RED_LIGHT;
    }

    private static String getColorBlueLight() {
        return isDarkMode ? DARK_COLOR_BLUE_LIGHT : LIGHT_COLOR_BLUE_LIGHT;
    }

    private static String getColorOrangeLight() {
        return isDarkMode ? DARK_COLOR_ORANGE_LIGHT : LIGHT_COLOR_ORANGE_LIGHT;
    }

    private static String getColorGrayBackground() {
        return isDarkMode ? DARK_COLOR_GRAY_BACKGROUND : LIGHT_COLOR_GRAY_BACKGROUND;
    }

    private static String getColorSeparator() {
        return isDarkMode ? DARK_COLOR_SEPARATOR : LIGHT_COLOR_SEPARATOR;
    }

    private static String getColorCardBackground() {
        return isDarkMode ? DARK_COLOR_CARD_BACKGROUND : LIGHT_COLOR_PRIMARY;
    }

    // Load thư viện native
    static {
        try {
            System.loadLibrary("Client");
        } catch (Exception e) {
            Log.e(TAG, "Error loading library: " + e.getMessage());
        }
    }

    // Native method declarations for Game Guardian detection
    public static native void nativeSetupAntiDebug();
    public static native boolean nativeDetectGameGuardian();
    public static native boolean nativeDetectMemoryTampering();
    public static native boolean nativeDetectSuspiciousProcesses();

    // Class để lưu trữ thông tin trạng thái mod
    private static class ModStatus {
        String version;
        String safety;
        String safetyMessage;
        String description;
        String icon;
        String title;
        String status;
        boolean vpnCheckEnabled;

        ModStatus(JSONObject json) {
            try {
                this.version = json.optString("version", "unknown");
                this.safety = json.optString("safety", "unknown");
                this.safetyMessage = json.optString("safety_message", "");
                this.description = json.optString("description", "");
                this.icon = json.optString("icon", "");
                this.title = json.optString("title", "");
                this.status = json.optString("status", "not_ready");
                this.vpnCheckEnabled = json.optBoolean("vpn_check_enabled", true); // Mặc định bật VPN check
            } catch (Exception e) {
                Log.e(TAG, "Error parsing ModStatus: " + e.getMessage());
                // Đặt giá trị mặc định nếu có lỗi
                this.version = "unknown";
                this.safety = "unknown";
                this.safetyMessage = "";
                this.description = "";
                this.icon = "";
                this.title = "Unknown Mod";
                this.status = "not_ready";
                this.vpnCheckEnabled = true; // Mặc định bật VPN check để an toàn
            }
        }
    }

    // Khai báo các phương thức native
    public native static String LibOnline(Context mContext, String path);
    private native static void CheckOverlayPermission(Context context);
    public native static String CheckModStatus();
    public native static String GetAppIdentifier(); // Phương thức mới để lấy APP_IDENTIFIER

    // 🛡️ GAME GUARDIAN DETECTION - Native methods
    private native static int nativeDetectGameGuardian();
    private native static boolean nativeDetectMemoryTampering();
    private native static int nativeSetupAntiDebug();
    private native static boolean nativeDetectSuspiciousProcesses();

    // Handler và Runnable để kiểm tra VPN định kỳ
    private static Handler vpnCheckHandler;
    private static Runnable vpnCheckRunnable;
    private static boolean isVpnCheckActive = false;
    private static long vpnCheckStartTime = 0; // Thời gian bắt đầu VPN check

    // ✅ ENHANCED VPN MONITORING: Continuous check from mod selection to app close
    private static boolean isContinuousVpnCheckEnabled = false; // Kiểm tra liên tục từ chọn mod đến tắt app
    private static final long VPN_CHECK_INTERVAL_FAST = 3000; // 3 giây - check nhanh khi đang tải
    private static final long VPN_CHECK_INTERVAL_NORMAL = 5000; // 5 giây - check bình thường khi đã vào menu
    private static final long VPN_CHECK_INTERVAL_BACKGROUND = 10000; // 10 giây - check khi app ở background

    // 🛡️ ANTI-TAMPERING PROTECTION SYSTEM
    private static final String[] INTEGRITY_CHECKSUMS = {
        "vpn_check_method_hash_1", "vpn_check_method_hash_2", "vpn_check_method_hash_3"
    };
    private static volatile boolean[] integrityFlags = {true, true, true, true, true}; // Multiple flags for redundancy
    private static volatile long lastIntegrityCheck = 0;
    private static final long INTEGRITY_CHECK_INTERVAL = 2000; // Check every 2 seconds
    private static volatile int tamperDetectionCount = 0;
    private static final int MAX_TAMPER_ATTEMPTS = 3;

    // Memory monitoring - Tăng interval để giảm tải
    private static long lastMemoryCheck = 0;
    private static final long MEMORY_CHECK_INTERVAL = 60000; // Tăng từ 30s lên 60s

    // Thêm các Handler để quản lý lifecycle
    private static final List<Handler> activeHandlers = new ArrayList<>();
    private static final List<Thread> activeThreads = new ArrayList<>();

    // Phương thức khởi động chính
    public static void Start(Context context) {
        CrashHandler.init(context, true);
        if (context == null) {
            Log.e(TAG, "Context is null");
            return;
        }

        try {
            if (context instanceof Activity) {
                mContextRef = new WeakReference<>(context);
                loadSettings();
                checkAndCreateCacheDir();

                // 🛡️ GAME GUARDIAN DETECTION - Chạy trước khi show mod selector
                performGameGuardianCheck();

                // OPTIMIZED: Chỉ check VPN khi cần thiết dựa trên server response
                // VPN check sẽ được điều khiển bởi dữ liệu từ server
                Log.d(TAG, "App started - VPN check will be controlled by server data");

                showModSelector();
            } else {
                CheckOverlayPermission(context);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in Start method: " + e.getMessage());
            showError(context, "Khởi động thất bại: " + e.getMessage());
        }
    }

    /**
     * Bắt đầu kiểm tra VPN định kỳ
     */
    private static synchronized void startVpnCheck() {
        if (isVpnCheckActive) {
            return; // Đã đang chạy rồi
        }

        vpnCheckHandler = new Handler(Looper.getMainLooper());
        activeHandlers.add(vpnCheckHandler); // Track handler để cleanup
        vpnCheckStartTime = System.currentTimeMillis(); // Ghi lại thời gian bắt đầu
        vpnCheckRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // Kiểm tra thời gian chạy - tự động dừng sau 5 phút để tránh crash
                    long currentTime = System.currentTimeMillis();
                    long MAX_VPN_CHECK_DURATION = 0;
                    if (currentTime - vpnCheckStartTime > MAX_VPN_CHECK_DURATION)
                    {
                        Log.i(TAG, "VPN check auto-stopped after 5 minutes to prevent crash");
                        stopVpnCheck();
                        return;
                    }

                    // Kiểm tra context còn hợp lệ không
                    Context context = getContext();
                    if (context == null) {
                        Log.w(TAG, "Context is null, stopping VPN check");
                        stopVpnCheck();
                        return;
                    }

                    // Kiểm tra activity còn active không
                    Activity activity = getActivity();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                        Log.w(TAG, "Activity is not active, stopping VPN check");
                        stopVpnCheck();
                        return;
                    }

                    // OPTIMIZED: Chỉ kiểm tra VPN nếu đã chọn mod và mod yêu cầu VPN check
                    if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED) {
                        if (isVpnEnabled()) {
                            Log.e(TAG, "VPN detected during app usage (VPN check enabled for current mod)");

                            // Hiển thị thông báo
                            showError(context, "Phát hiện VPN đang bật! Ứng dụng sẽ đóng.");

                            // Dừng kiểm tra VPN trước
                            stopVpnCheck();

                            // Đóng ứng dụng sau 2 giây sử dụng handler có sẵn
                            Handler closeHandler = new Handler(Looper.getMainLooper());
                            activeHandlers.add(closeHandler);
                            closeHandler.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        Activity act = getActivity();
                                        if (act != null && !act.isFinishing() && !act.isDestroyed()) {
                                            act.finish();
                                            android.os.Process.killProcess(android.os.Process.myPid());
                                            System.exit(1);
                                        }
                                    }
                                }, 2000);

                            return;
                        }
                    } else {
                        // OPTIMIZED: Không check VPN nếu chưa chọn mod hoặc mod cho phép VPN
                        // Log.d(TAG, "VPN check skipped - no mod selected or VPN allowed");
                    }

                    // Memory check định kỳ
                    long memoryCheckTime = System.currentTimeMillis();
                    if (memoryCheckTime - lastMemoryCheck > MEMORY_CHECK_INTERVAL) {
                        lastMemoryCheck = memoryCheckTime;
                        checkMemoryUsage();
                    }

                    // OPTIMIZED: Chỉ lập lịch tiếp theo nếu VPN check vẫn active và đang trong quá trình tải mod
                    if (isVpnCheckActive && vpnCheckHandler != null && context != null &&
                        MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED) {
                        vpnCheckHandler.postDelayed(this, 10000); // Check mỗi 10s trong quá trình tải
                    } else {
                        // Dừng VPN check nếu không còn cần thiết
                        Log.d(TAG, "Stopping VPN check - no longer needed");
                        stopVpnCheck();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in VPN check: " + e.getMessage());
                    // Dừng VPN check nếu có lỗi để tránh crash liên tục
                    stopVpnCheck();
                }
            }
        };

        // Bắt đầu kiểm tra ngay lập tức
        vpnCheckHandler.post(vpnCheckRunnable);
        isVpnCheckActive = true;
        Log.d(TAG, "VPN check started");
    }

    /**
     * ✅ ENHANCED: Bắt đầu kiểm tra VPN liên tục từ lúc chọn mod đến khi tắt app
     */
    private static synchronized void startContinuousVpnCheck() {
        if (isVpnCheckActive) {
            return; // Đã đang chạy rồi
        }

        isContinuousVpnCheckEnabled = true;
        vpnCheckHandler = new Handler(Looper.getMainLooper());
        activeHandlers.add(vpnCheckHandler);
        vpnCheckStartTime = System.currentTimeMillis();

        vpnCheckRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    // Kiểm tra context còn hợp lệ không
                    Context context = getContext();
                    if (context == null) {
                        Log.w(TAG, "Context is null, stopping continuous VPN check");
                        stopContinuousVpnCheck();
                        return;
                    }

                    // Kiểm tra activity còn active không
                    Activity activity = getActivity();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                        Log.w(TAG, "Activity is not active, stopping continuous VPN check");
                        stopContinuousVpnCheck();
                        return;
                    }

                    // ✅ ENHANCED: Kiểm tra VPN liên tục nếu mod yêu cầu và đã được chọn
                    if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED && isContinuousVpnCheckEnabled) {

                        // 🛡️ ANTI-TAMPERING: Perform periodic integrity check
                        performPeriodicIntegrityCheck();

                        // 🛡️ SECURE VPN CHECK: Use multiple redundant methods
                        if (isVpnEnabledSecure()) {
                            Log.e(TAG, "🚨 SECURE VPN CHECK: VPN detected! Terminating app immediately.");

                            // Hiển thị thông báo ngắn gọn
                            showError(context, "🚨 Phát hiện VPN! App sẽ đóng ngay lập tức.");

                            // Dừng kiểm tra VPN trước
                            stopContinuousVpnCheck();

                            // 🛡️ SECURE TERMINATION: Use secure termination method
                            terminateAppImmediately("VPN detected during continuous monitoring");

                            return;
                        }
                    }

                    // Memory check định kỳ
                    long memoryCheckTime = System.currentTimeMillis();
                    if (memoryCheckTime - lastMemoryCheck > MEMORY_CHECK_INTERVAL) {
                        lastMemoryCheck = memoryCheckTime;
                        checkMemoryUsage();
                    }

                    // ✅ ENHANCED: Lập lịch check tiếp theo với interval phù hợp
                    if (isContinuousVpnCheckEnabled && vpnCheckHandler != null && context != null &&
                        MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED) {

                        long interval = VPN_CHECK_INTERVAL_NORMAL; // Default 5 giây

                        // Điều chỉnh interval dựa trên trạng thái app
                        if (isLoading) {
                            interval = VPN_CHECK_INTERVAL_FAST; // 3 giây khi đang tải
                        } else if (!activity.hasWindowFocus()) {
                            interval = VPN_CHECK_INTERVAL_BACKGROUND; // 10 giây khi ở background
                        }

                        vpnCheckHandler.postDelayed(this, interval);
                        Log.d(TAG, "Continuous VPN check scheduled with " + interval + "ms interval");
                    } else {
                        // Dừng VPN check nếu không còn cần thiết
                        Log.d(TAG, "Stopping continuous VPN check - no longer needed");
                        stopContinuousVpnCheck();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error in continuous VPN check: " + e.getMessage());
                    // Dừng VPN check nếu có lỗi để tránh crash liên tục
                    stopContinuousVpnCheck();
                }
            }
        };

        // Bắt đầu kiểm tra ngay lập tức
        vpnCheckHandler.post(vpnCheckRunnable);
        isVpnCheckActive = true;
        Log.d(TAG, "✅ Continuous VPN check started - will run until app closes");
    }

    /**
     * Dừng kiểm tra VPN định kỳ
     */
    private static synchronized void stopVpnCheck() {
        if (vpnCheckHandler != null && vpnCheckRunnable != null) {
            vpnCheckHandler.removeCallbacks(vpnCheckRunnable);
            isVpnCheckActive = false;
            Log.d(TAG, "VPN check stopped");
        }
    }

    /**
     * ✅ ENHANCED: Dừng kiểm tra VPN liên tục
     */
    private static synchronized void stopContinuousVpnCheck() {
        isContinuousVpnCheckEnabled = false;
        if (vpnCheckHandler != null && vpnCheckRunnable != null) {
            vpnCheckHandler.removeCallbacks(vpnCheckRunnable);
            isVpnCheckActive = false;
            Log.d(TAG, "✅ Continuous VPN check stopped");
        }
    }

    // 🛡️ ANTI-TAMPERING PROTECTION METHODS

    /**
     * 🛡️ Kiểm tra tính toàn vẹn của code VPN check
     */
    private static boolean verifyCodeIntegrity() {
        try {
            // 🛡️ ENHANCED: Use advanced checksum validation
            if (!validateMethodChecksums()) {
                Log.e(TAG, "🚨 ADVANCED TAMPERING DETECTED: Method checksum validation failed!");
                return false;
            }

            // Kiểm tra xem các method quan trọng có bị modify không
            Class<?> clazz = Main.class;

            // Kiểm tra method signatures
            java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();
            int vpnMethodCount = 0;
            int integrityMethodCount = 0;

            for (java.lang.reflect.Method method : methods) {
                String methodName = method.getName();
                if (methodName.contains("vpn") || methodName.contains("Vpn") || methodName.contains("VPN")) {
                    vpnMethodCount++;
                }
                if (methodName.contains("integrity") || methodName.contains("Integrity") ||
                    methodName.contains("tamper") || methodName.contains("Tamper")) {
                    integrityMethodCount++;
                }
            }

            // Nếu số lượng method VPN quá ít, có thể đã bị xóa
            if (vpnMethodCount < 8) { // Increased threshold due to new methods
                Log.e(TAG, "🚨 TAMPERING DETECTED: VPN methods missing or modified! Found: " + vpnMethodCount);
                return false;
            }

            // Kiểm tra các flag integrity
            for (boolean flag : integrityFlags) {
                if (!flag) {
                    Log.e(TAG, "🚨 TAMPERING DETECTED: Integrity flag compromised!");
                    return false;
                }
            }

            // 🛡️ Additional check: Verify class structure hasn't been modified
            if (clazz.getDeclaredFields().length < 20) { // Assuming we have at least 20 fields
                Log.e(TAG, "🚨 TAMPERING DETECTED: Class structure modified!");
                return false;
            }

            return true;
        } catch (Exception e) {
            Log.e(TAG, "🚨 TAMPERING DETECTED: Exception during integrity check: " + e.getMessage());
            return false;
        }
    }

    /**
     * 🛡️ Obfuscated VPN check method 1
     */
    private static boolean checkNetworkInterface_A() {
        try {
            // Obfuscated method name và logic
            java.util.Enumeration<java.net.NetworkInterface> interfaces =
                java.net.NetworkInterface.getNetworkInterfaces();

            while (interfaces.hasMoreElements()) {
                java.net.NetworkInterface networkInterface = interfaces.nextElement();
                String name = networkInterface.getDisplayName().toLowerCase();

                // Obfuscated VPN detection strings
                String[] vpnKeywords = {"tun", "ppp", "pptp", "l2tp", "vpn", "tap"};
                for (String keyword : vpnKeywords) {
                    if (name.contains(keyword)) {
                        return true; // VPN detected
                    }
                }
            }
            return false;
        } catch (Exception e) {
            // Nếu có exception, coi như có VPN để an toàn
            return true;
        }
    }

    /**
     * 🛡️ Obfuscated VPN check method 2
     */
    private static boolean checkNetworkInterface_B() {
        try {
            // Alternative VPN detection method
            java.util.List<java.net.NetworkInterface> interfaces =
                java.util.Collections.list(java.net.NetworkInterface.getNetworkInterfaces());

            for (java.net.NetworkInterface ni : interfaces) {
                if (ni.isUp() && !ni.isLoopback()) {
                    String name = ni.getName().toLowerCase();
                    // Different obfuscated keywords
                    if (name.startsWith("tun") || name.startsWith("tap") ||
                        name.startsWith("ppp") || name.contains("vpn")) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            return true; // Assume VPN if error
        }
    }

    /**
     * 🛡️ Obfuscated VPN check method 3 - System properties check
     */
    private static boolean checkSystemProperties_C() {
        try {
            // Check system properties for VPN indicators
            String[] vpnProps = {
                "net.dns1", "net.dns2", "net.dnschange", "net.dns.secure"
            };

            for (String prop : vpnProps) {
                String value = System.getProperty(prop);
                if (value != null && (value.contains("*******") || value.contains("*******"))) {
                    // Common VPN DNS servers
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 🛡️ Master VPN check với multiple redundant methods
     */
    private static boolean isVpnEnabledSecure() {
        // Kiểm tra integrity trước
        if (!verifyCodeIntegrity()) {
            tamperDetectionCount++;
            if (tamperDetectionCount >= MAX_TAMPER_ATTEMPTS) {
                Log.e(TAG, "🚨 CRITICAL: Multiple tampering attempts detected! Terminating app.");
                terminateAppImmediately("Code tampering detected");
                return true; // Force VPN detected to close app
            }
        }

        // Sử dụng multiple methods để detect VPN
        boolean method1 = checkNetworkInterface_A();
        boolean method2 = checkNetworkInterface_B();
        boolean method3 = checkSystemProperties_C();
        boolean originalMethod = isVpnEnabled(); // Original method

        // Nếu bất kỳ method nào detect VPN, return true
        boolean vpnDetected = method1 || method2 || method3 || originalMethod;

        // Log để debug (có thể remove trong production)
        if (vpnDetected) {
            Log.w(TAG, String.format("🚨 VPN detected by methods: A=%b, B=%b, C=%b, Original=%b",
                                     method1, method2, method3, originalMethod));
        }

        return vpnDetected;
    }

    /**
     * 🛡️ Terminate app immediately với multiple methods
     */
    private static void terminateAppImmediately(String reason) {
        try {
            Log.e(TAG, "🚨 TERMINATING APP: " + reason);

            // Method 1: Standard finish
            Activity activity = getActivity();
            if (activity != null && !activity.isFinishing()) {
                activity.finish();
            }

            // Method 2: Kill process
            android.os.Process.killProcess(android.os.Process.myPid());

            // Method 3: System exit
            System.exit(1);

            // Method 4: Runtime halt (backup)
            Runtime.getRuntime().halt(1);

        } catch (Exception e) {
            // If all else fails, force crash
            throw new RuntimeException("Security violation: " + reason);
        }
    }

    /**
     * 🛡️ Periodic integrity check
     */
    private static void performPeriodicIntegrityCheck() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastIntegrityCheck > INTEGRITY_CHECK_INTERVAL) {
            lastIntegrityCheck = currentTime;

            // Random integrity checks
            if (!verifyCodeIntegrity()) {
                Log.e(TAG, "🚨 PERIODIC INTEGRITY CHECK FAILED!");
                terminateAppImmediately("Periodic integrity check failed");
            }

            // Verify integrity flags haven't been tampered with
            if (integrityFlags.length != 5) {
                Log.e(TAG, "🚨 INTEGRITY FLAGS ARRAY TAMPERED!");
                terminateAppImmediately("Integrity flags array modified");
            }

            // Check for suspicious method count changes
            try {
                Class<?> clazz = Main.class;
                java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();
                if (methods.length < 50) { // Assuming we have at least 50 methods
                    Log.e(TAG, "🚨 SUSPICIOUS METHOD COUNT: " + methods.length);
                    terminateAppImmediately("Method count too low - possible tampering");
                }
            } catch (Exception e) {
                terminateAppImmediately("Exception during method count check");
            }
        }
    }

    /**
     * Gọi khi ứng dụng được resume (trở lại foreground)
     * Phương thức này nên được gọi từ Activity.onResume()
     */
    public static void onAppResume() {
        Log.d(TAG, "App resumed");

        // 🛡️ ANTI-TAMPERING: Verify integrity on resume
        if (!verifyCodeIntegrity()) {
            Log.e(TAG, "🚨 INTEGRITY CHECK FAILED ON RESUME!");
            terminateAppImmediately("Integrity check failed on app resume");
            return;
        }

        // ✅ ENHANCED: Khởi động lại continuous VPN check nếu mod đã được chọn và yêu cầu VPN check
        if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED && !isVpnCheckActive) {
            Log.d(TAG, "✅ Resuming continuous VPN check for selected mod");
            startContinuousVpnCheck();
        } else {
            Log.d(TAG, "App resumed - VPN check not needed or already active");
        }
    }

    /**
     * Gọi khi ứng dụng bị pause (chuyển sang background)
     * Phương thức này nên được gọi từ Activity.onPause()
     */
    public static void onAppPause() {
        Log.d(TAG, "App paused");

        // ✅ ENHANCED: Không dừng continuous VPN check khi pause
        // Continuous VPN check sẽ tiếp tục chạy với interval chậm hơn
        if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED && isContinuousVpnCheckEnabled) {
            Log.d(TAG, "✅ Continuous VPN check continues in background with slower interval");
        } else {
            // Chỉ dừng VPN check thông thường (không phải continuous)
            stopVpnCheck();
        }
    }

    /**
     * Gọi khi ứng dụng bị destroy (đóng hoàn toàn)
     * Phương thức này nên được gọi từ Activity.onDestroy()
     */
    public static void onAppDestroy() {
        Log.d(TAG, "App destroyed");

        // ✅ ENHANCED: Dừng cả VPN check thông thường và continuous VPN check
        stopVpnCheck();
        stopContinuousVpnCheck();
        vpnCheckHandler = null;
        vpnCheckRunnable = null;

        // Cleanup tất cả handlers và threads
        cleanupResources();
    }

    /**
     * Dừng tất cả background processes sau khi login thành công để tránh crash
     */
    private static void stopBackgroundProcessesAfterLogin() {
        try {
            Log.d(TAG, "Stopping background processes after successful login");

            // OPTIMIZED: Dừng VPN check sau khi login thành công vào menu để giảm lag
            // VPN check sẽ được khởi động lại khi cần thiết dựa trên server response
            stopVpnCheck();
            Log.d(TAG, "VPN check stopped after login - will restart based on server requirements");

            // Cleanup tất cả animation handlers
            stopAllAnimations();

            // Cleanup progress handlers
            for (int i = activeHandlers.size() - 1; i >= 0; i--) {
                Handler handler = activeHandlers.get(i);
                if (handler != null) {
                    handler.removeCallbacksAndMessages(null);
                    activeHandlers.remove(i);
                }
            }

            Log.d(TAG, "Background processes stopped successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping background processes: " + e.getMessage());
        }
    }

    /**
     * Cleanup tất cả tài nguyên để tránh memory leak
     */
    private static void cleanupResources() {
        try {
            Log.d(TAG, "Starting resource cleanup");

            // Dừng VPN check trước tiên
            stopVpnCheck();

            // Cleanup handlers
            for (Handler handler : activeHandlers) {
                if (handler != null) {
                    handler.removeCallbacksAndMessages(null);
                }
            }
            activeHandlers.clear();

            // Cleanup threads với timeout
            for (Thread thread : activeThreads) {
                if (thread != null && thread.isAlive()) {
                    thread.interrupt();
                    try {
                        thread.join(1000); // Đợi tối đa 1 giây
                    } catch (InterruptedException e) {
                        Log.w(TAG, "Thread join interrupted: " + e.getMessage());
                    }
                }
            }
            activeThreads.clear();

            // Cleanup animations
            stopAllAnimations();

            // Cleanup dialogs
            if (currentDialog != null) {
                try {
                    if (currentDialog.isShowing()) {
                        currentDialog.dismiss();
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Error dismissing dialog: " + e.getMessage());
                }
                currentDialog = null;
            }

            // Force cleanup tất cả handlers ngay lập tức
            for (Handler handler : activeHandlers) {
                if (handler != null) {
                    handler.removeCallbacksAndMessages(null);
                }
            }

            // Cleanup overlay
            hideGlobalBlockingOverlay();
            globalBlockingOverlay = null;

            // Reset flags
            isDialogShowing = false;
            isLoading = false;
            MOD_SELECTED = false;
            CURRENT_MOD_VPN_CHECK_ENABLED = false;

            // ✅ ENHANCED: Reset continuous VPN check flags
            isContinuousVpnCheckEnabled = false;

            Log.d(TAG, "Resource cleanup completed");

        } catch (Exception e) {
            Log.e(TAG, "Error cleaning up resources: " + e.getMessage());
        }
    }

    // 🛡️ OBFUSCATION: Dummy methods to confuse reverse engineers

    /**
     * Dummy VPN check method 1 - Always returns false
     */
    private static boolean isVpnActive() {
        // This is a decoy method
        return false;
    }

    /**
     * Dummy VPN check method 2 - Always returns false
     */
    private static boolean checkVpnConnection() {
        // This is a decoy method
        return false;
    }

    /**
     * Dummy VPN check method 3 - Always returns false
     */
    private static boolean detectVpnUsage() {
        // This is a decoy method
        return false;
    }

    /**
     * Dummy disable method - Does nothing
     */
    private static void disableVpnCheck() {
        // This is a decoy method - does nothing
        Log.d(TAG, "Decoy method called - no effect");
    }

    /**
     * Dummy bypass method - Does nothing
     */
    private static void bypassVpnDetection() {
        // This is a decoy method - does nothing
        Log.d(TAG, "Decoy bypass method called - no effect");
    }

    /**
     * Fake integrity check that always returns true
     */
    private static boolean checkIntegrity() {
        // This is a decoy method
        return true;
    }

    /**
     * 🛡️ REAL integrity verification with obfuscated name
     */
    private static boolean validateSystemState() {
        // This is the real integrity check with obfuscated name
        return verifyCodeIntegrity();
    }

    /**
     * 🛡️ Advanced checksum validation for critical methods
     */
    private static boolean validateMethodChecksums() {
        try {
            Class<?> clazz = Main.class;

            // Get critical methods and calculate their "signatures"
            java.lang.reflect.Method[] methods = clazz.getDeclaredMethods();

            int criticalMethodCount = 0;
            int secureMethodCount = 0;

            for (java.lang.reflect.Method method : methods) {
                String methodName = method.getName();

                // Count critical VPN-related methods
                if (methodName.contains("vpn") || methodName.contains("Vpn") ||
                    methodName.contains("VPN") || methodName.contains("Network")) {
                    criticalMethodCount++;
                }

                // Count security-related methods
                if (methodName.contains("integrity") || methodName.contains("Integrity") ||
                    methodName.contains("secure") || methodName.contains("Secure") ||
                    methodName.contains("tamper") || methodName.contains("Tamper") ||
                    methodName.contains("validate") || methodName.contains("Validate")) {
                    secureMethodCount++;
                }
            }

            // Verify expected method counts (adjust these numbers based on actual implementation)
            if (criticalMethodCount < 5) {
                Log.e(TAG, "🚨 CRITICAL METHODS MISSING: Expected >=5, found " + criticalMethodCount);
                return false;
            }

            if (secureMethodCount < 3) {
                Log.e(TAG, "🚨 SECURITY METHODS MISSING: Expected >=3, found " + secureMethodCount);
                return false;
            }

            // Additional validation: Check for suspicious method modifications
            for (java.lang.reflect.Method method : methods) {
                String methodName = method.getName();

                // If someone tries to add obvious bypass methods, detect them
                if (methodName.equals("disableVpnCheck") || methodName.equals("bypassVpnDetection") ||
                    methodName.equals("skipVpnCheck") || methodName.equals("ignoreVpn")) {

                    // Check if these are our decoy methods or malicious additions
                    if (!isDecoyMethod(method)) {
                        Log.e(TAG, "🚨 MALICIOUS METHOD DETECTED: " + methodName);
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            Log.e(TAG, "🚨 EXCEPTION DURING CHECKSUM VALIDATION: " + e.getMessage());
            return false;
        }
    }

    /**
     * 🛡️ Check if a method is one of our decoy methods
     */
    private static boolean isDecoyMethod(java.lang.reflect.Method method) {
        try {
            // Our decoy methods should have specific characteristics
            String methodName = method.getName();

            // Check if it's one of our known decoy methods
            if (methodName.equals("disableVpnCheck") || methodName.equals("bypassVpnDetection") ||
                methodName.equals("isVpnActive") || methodName.equals("checkVpnConnection") ||
                methodName.equals("detectVpnUsage") || methodName.equals("checkIntegrity")) {

                // Verify the method body is empty or returns false/true as expected
                // This is a simplified check - in production you might want more sophisticated validation
                return true;
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra memory usage và cleanup nếu cần thiết
     */
    private static void checkMemoryUsage() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            long availableMemory = maxMemory - usedMemory;

            double memoryUsagePercent = (double) usedMemory / maxMemory * 100;

            Log.d(TAG, String.format("Memory usage: %.1f%% (%d MB / %d MB)",
                                     memoryUsagePercent, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024));

            // Nếu memory usage > 80%, thực hiện cleanup
            if (memoryUsagePercent > 80) {
                Log.w(TAG, "High memory usage detected, performing cleanup");

                // Cleanup handlers cũ
                for (int i = activeHandlers.size() - 1; i >= 0; i--) {
                    Handler handler = activeHandlers.get(i);
                    if (handler != null) {
                        handler.removeCallbacksAndMessages(null);
                        activeHandlers.remove(i);
                    }
                }

                // Force garbage collection
                System.gc();

                // Log memory sau cleanup
                long newUsedMemory = runtime.totalMemory() - runtime.freeMemory();
                double newMemoryUsagePercent = (double) newUsedMemory / maxMemory * 100;
                Log.d(TAG, String.format("Memory after cleanup: %.1f%% (%d MB / %d MB)",
                                         newMemoryUsagePercent, newUsedMemory / 1024 / 1024, maxMemory / 1024 / 1024));
            }

            // Nếu memory usage > 90%, dừng VPN check để tránh crash
            if (memoryUsagePercent > 90) {
                Log.e(TAG, "Critical memory usage, stopping all VPN checks");
                stopVpnCheck();
                stopContinuousVpnCheck(); // ✅ ENHANCED: Also stop continuous VPN check
            }

        } catch (Exception e) {
            Log.e(TAG, "Error checking memory usage: " + e.getMessage());
        }
    }

    // 🛡️ GAME GUARDIAN DETECTOR CLASS
    public static class GameGuardianDetector {
        private static final String TAG = "GGDetector";

        public static class DetectionResult {
            public boolean detected = false;
            public int confidence = 0;
            public String method = "";
            public String details = "";

            public DetectionResult(boolean detected, int confidence, String method, String details) {
                this.detected = detected;
                this.confidence = confidence;
                this.method = method;
                this.details = details;
            }
        }

        /**
         * 🛡️ MASTER DETECTION METHOD - Hybrid Java + Native
         * Phương pháp chính xác nhất để phát hiện Game Guardian
         */
        public static DetectionResult detectGameGuardian() {
            try {
                Log.d(TAG, "🛡️ Starting Game Guardian detection...");

                // Step 1: Setup anti-debug protection
                int antiDebugResult = nativeSetupAntiDebug();
                if (antiDebugResult == 1) {
                    return new DetectionResult(true, 98,
                        "Anti-Debug Protection",
                        "Process already being traced - Game Guardian detected");
                }

                // Step 2: Primary detection - Native TracerPid check
                int nativeConfidence = nativeDetectGameGuardian();
                Log.d(TAG, "Native detection confidence: " + nativeConfidence + "%");

                if (nativeConfidence >= 75) {
                    return new DetectionResult(true, nativeConfidence,
                        "Native TracerPid Detection",
                        "Process attachment detected by native code");
                }

                // Step 3: Memory tampering detection
                if (nativeDetectMemoryTampering()) {
                    return new DetectionResult(true, 90,
                        "Native Memory Protection",
                        "Memory tampering detected");
                }

                // Step 4: Suspicious processes detection
                if (nativeDetectSuspiciousProcesses()) {
                    return new DetectionResult(true, 80,
                        "Native Process Analysis",
                        "Suspicious processes detected");
                }

                // Step 5: Java-based backup detection
                if (detectGameGuardianJava()) {
                    return new DetectionResult(true, 70,
                        "Java Backup Detection",
                        "Suspicious behavior detected by Java layer");
                }

                // No detection
                Log.d(TAG, "✅ No Game Guardian detected - Device is clean");
                return new DetectionResult(false, 0, "Clean",
                    "No Game Guardian detected");

            } catch (Exception e) {
                Log.e(TAG, "Detection error: " + e.getMessage());
                return new DetectionResult(false, 0, "Error",
                    "Detection failed: " + e.getMessage());
            }
        }

        /**
         * Quick detection - Chỉ dùng native TracerPid (nhanh nhất)
         */
        public static boolean quickDetect() {
            try {
                int confidence = nativeDetectGameGuardian();
                return confidence >= 75;
            } catch (Exception e) {
                Log.e(TAG, "Quick detection error: " + e.getMessage());
                return false;
            }
        }

        /**
         * Java-based backup detection methods
         */
        private static boolean detectGameGuardianJava() {
            try {
                return detectSuspiciousProcessNames() ||
                       detectAbnormalSystemBehavior();
            } catch (Exception e) {
                Log.e(TAG, "Java detection error: " + e.getMessage());
                return false;
            }
        }

        /**
         * Phát hiện process names nghi ngờ
         */
        private static boolean detectSuspiciousProcessNames() {
            try {
                Process ps = Runtime.getRuntime().exec("ps -A -o comm");
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(ps.getInputStream()));

                String line;
                int suspiciousCount = 0;

                while ((line = reader.readLine()) != null) {
                    line = line.trim().toLowerCase();

                    // Tìm process names nghi ngờ
                    if (line.length() <= 3 ||  // Tên quá ngắn
                        line.equals("gg") ||
                        line.equals("cih") ||
                        line.matches(".*[0-9]{3,}.*")) {  // Chứa nhiều số
                        suspiciousCount++;
                    }
                }
                reader.close();

                return suspiciousCount > 2; // > 2 process nghi ngờ
            } catch (Exception e) {
                return false;
            }
        }

        /**
         * Phát hiện hành vi bất thường của hệ thống
         */
        private static boolean detectAbnormalSystemBehavior() {
            try {
                // Kiểm tra CPU usage pattern
                Runtime runtime = Runtime.getRuntime();
                long startTime = System.currentTimeMillis();

                // Đo CPU usage trong 2 giây
                for (int i = 0; i < 20; i++) {
                    runtime.gc(); // Trigger GC để test
                    Thread.sleep(100);
                }

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                // Nếu mất quá lâu = có tool đang can thiệp
                return duration > 3000; // > 3 giây = nghi ngờ

            } catch (Exception e) {
                return false;
            }
        }
    }

    // Phương thức để lưu cài đặt
    private static void saveSettings() {
        Context context = getContext();
        if (context == null) return;

        try {
            SharedPreferences sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putBoolean(PREF_DARK_MODE, isDarkMode);
            editor.apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving settings: " + e.getMessage());
        }
    }

    // Phương thức để tải cài đặt
    private static void loadSettings() {
        Context context = getContext();
        if (context == null) return;

        try {
            SharedPreferences sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            isDarkMode = sharedPreferences.getBoolean(PREF_DARK_MODE, false);
        } catch (Exception e) {
            Log.e(TAG, "Error loading settings: " + e.getMessage());
            isDarkMode = false;
        }
    }

    // Phương thức hỗ trợ để lấy Context hiện tại
    private static Context getContext() {
        if (mContextRef != null) {
            Context context = mContextRef.get();
            if (context != null) {
                return context;
            }
        }
        return null;
    }

    // Phương thức hỗ trợ để lấy Activity hiện tại
    private static Activity getActivity() {
        Context context = getContext();
        if (context instanceof Activity) {
            return (Activity) context;
        }
        return null;
    }

    // Dừng tất cả các animation để tránh memory leak
    private static void stopAllAnimations() {
        animationShouldRun = false;
        if (arrowAnimHandler != null) {
            arrowAnimHandler.removeCallbacksAndMessages(null);
            arrowAnimHandler = null;
        }
        if (rotateAnimation != null) {
            rotateAnimation.cancel();
            rotateAnimation = null;
        }
        if (loadingGearIcon != null) {
            loadingGearIcon.clearAnimation();
        }
    }

    private static void showGlobalBlockingOverlay() {
        try {
            if (globalBlockingOverlay == null) {
                Context context = getContext();
                if (context == null) return;

                globalBlockingOverlay = new View(context);
                globalBlockingOverlay.setLayoutParams(new ViewGroup.LayoutParams(
                                                          ViewGroup.LayoutParams.MATCH_PARENT,
                                                          ViewGroup.LayoutParams.MATCH_PARENT
                                                      ));
                globalBlockingOverlay.setBackgroundColor(Color.TRANSPARENT);
                globalBlockingOverlay.setClickable(true);
                globalBlockingOverlay.setFocusable(true);

                if (currentDialog != null && currentDialog.getWindow() != null) {
                    ViewGroup decorView = (ViewGroup) currentDialog.getWindow().getDecorView();
                    decorView.addView(globalBlockingOverlay);
                }
            }

            if (globalBlockingOverlay != null) {
                globalBlockingOverlay.setVisibility(View.VISIBLE);
            }
            isLoading = true;
        } catch (Exception e) {
            Log.e(TAG, "Error showing blocking overlay: " + e.getMessage());
        }
    }

    private static void hideGlobalBlockingOverlay() {
        try {
            if (globalBlockingOverlay != null) {
                globalBlockingOverlay.setVisibility(View.GONE);
            }
            isLoading = false;
        } catch (Exception e) {
            Log.e(TAG, "Error hiding blocking overlay: " + e.getMessage());
        }
    }

    private static void checkModStatus(final Runnable onComplete) {
        Thread checkThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    int retryCount = 0;
                    boolean success = false;

                    while (retryCount < MAX_RETRY_COUNT && !success && !Thread.currentThread().isInterrupted()) {
                        try {
                            String response = CheckModStatus();
                            if (response == null || response.isEmpty()) {
                                retryCount++;
                                if (retryCount >= MAX_RETRY_COUNT) {
                                    mainHandler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                Context context = getContext();
                                                if (context == null) return;

                                                showError(context, "Không thể kết nối tới máy chủ!");
                                                new Handler().postDelayed(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            Activity activity = getActivity();
                                                            if (activity != null && !activity.isFinishing()) {
                                                                activity.finish();
                                                                android.os.Process.killProcess(android.os.Process.myPid());
                                                                System.exit(1);
                                                            }
                                                        }
                                                    }, 2000);
                                            }
                                        });
                                } else {
                                    // Đợi trước khi thử lại
                                    Thread.sleep(1000 * retryCount);
                                }
                                continue;
                            }

                            try {
                                JSONObject json = new JSONObject(response);
                                if (!json.getString("status").equals("success")) {
                                    retryCount++;
                                    if (retryCount >= MAX_RETRY_COUNT) {
                                        final String errorMessage = json.optString("message", "Không thể lấy thông tin danh sách bản MOD. Vui lòng thử lại sau!");
                                        mainHandler.post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    Context context = getContext();
                                                    if (context == null) return;

                                                    showError(context, errorMessage);
                                                    new Handler().postDelayed(new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                Activity activity = getActivity();
                                                                if (activity != null && !activity.isFinishing()) {
                                                                    activity.finish();
                                                                    android.os.Process.killProcess(android.os.Process.myPid());
                                                                    System.exit(1);
                                                                }
                                                            }
                                                        }, 2000);
                                                }
                                            });
                                    } else {
                                        // Đợi trước khi thử lại
                                        Thread.sleep(1000 * retryCount);
                                    }
                                    continue;
                                }

                                JSONObject data = json.getJSONObject("data");
                                if (data.length() == 0) {
                                    mainHandler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                Context context = getContext();
                                                if (context == null) return;

                                                showError(context, "Không có thông tin MOD nào!");
                                                new Handler().postDelayed(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            Activity activity = getActivity();
                                                            if (activity != null && !activity.isFinishing()) {
                                                                activity.finish();
                                                                android.os.Process.killProcess(android.os.Process.myPid());
                                                                System.exit(1);
                                                            }
                                                        }
                                                    }, 2000);
                                            }
                                        });
                                    return;
                                }

                                modStatuses.clear();
                                Iterator<String> keys = data.keys();
                                while(keys.hasNext()) {
                                    String libName = keys.next();
                                    modStatuses.put(libName, new ModStatus(data.getJSONObject(libName)));
                                }

                                success = true;
                                mainHandler.post(onComplete);

                            } catch (Exception e) {
                                Log.e(TAG, "JSON parse error: " + e.getMessage());
                                retryCount++;
                                if (retryCount >= MAX_RETRY_COUNT) {
                                    final String errorMsg = e.getMessage();
                                    mainHandler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                Context context = getContext();
                                                if (context == null) return;

                                                showError(context, "Hiện tại không có MOD nào khả dụng , vui lòng thử lại sau!");
                                                new Handler().postDelayed(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            Activity activity = getActivity();
                                                            if (activity != null && !activity.isFinishing()) {
                                                                activity.finish();
                                                                android.os.Process.killProcess(android.os.Process.myPid());
                                                                System.exit(1);
                                                            }
                                                        }
                                                    }, 2000);
                                            }
                                        });
                                } else {
                                    // Đợi trước khi thử lại
                                    Thread.sleep(1000 * retryCount);
                                }
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Network error: " + e.getMessage());
                            retryCount++;
                            if (retryCount >= MAX_RETRY_COUNT) {
                                final String errorMsg = e.getMessage();
                                mainHandler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            Context context = getContext();
                                            if (context == null) return;

                                            showError(context, "Lỗi kết nối: " + (errorMsg != null ? errorMsg : "Không xác định"));
                                            new Handler().postDelayed(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        Activity activity = getActivity();
                                                        if (activity != null && !activity.isFinishing()) {
                                                            activity.finish();
                                                            android.os.Process.killProcess(android.os.Process.myPid());
                                                            System.exit(1);
                                                        }
                                                    }
                                                }, 2000);
                                        }
                                    });
                            } else {
                                try {
                                    // Đợi trước khi thử lại
                                    Thread.sleep(1000 * retryCount);
                                } catch (InterruptedException ie) {
                                    Log.e(TAG, "Sleep interrupted: " + ie.getMessage());
                                }
                            }
                        }
                    }
                }
            });

        // Track thread để cleanup sau này
        activeThreads.add(checkThread);
        checkThread.start();
    }
    private static View createLoadingOverlay() {
        Context context = getContext();
        if (context == null) return null;

        try {
            // Lấy kích thước màn hình
            DisplayMetrics metrics = new DisplayMetrics();
            Activity activity = getActivity();
            if (activity == null) return null;
            activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);

            final float density = metrics.density;
            final int screenWidth = metrics.widthPixels;

            // Main overlay
            FrameLayout overlay = new FrameLayout(context);
            overlay.setLayoutParams(new FrameLayout.LayoutParams(
                                        FrameLayout.LayoutParams.MATCH_PARENT,
                                        FrameLayout.LayoutParams.MATCH_PARENT
                                    ));
            overlay.setBackgroundColor(Color.parseColor("#80000000"));

            // Main dialog container
            LinearLayout mainContainer = new LinearLayout(context);
            mainContainer.setOrientation(LinearLayout.VERTICAL);

            // Thu nhỏ khung thông báo xuống 70% chiều rộng màn hình
            final int cardWidth = Math.min((int)(screenWidth * 0.7), (int)(350 * density));
            FrameLayout.LayoutParams containerParams = new FrameLayout.LayoutParams(
                cardWidth,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            containerParams.gravity = Gravity.CENTER;
            mainContainer.setLayoutParams(containerParams);

            // Đổ bóng cho container
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mainContainer.setElevation(24 * density);
            }

            // Background cho container chính
            GradientDrawable containerBg = new GradientDrawable();
            containerBg.setColor(Color.parseColor(getColorPrimary()));
            containerBg.setCornerRadius(24 * density);
            mainContainer.setBackground(containerBg);

            // Header với gradient
            LinearLayout headerContainer = new LinearLayout(context);
            headerContainer.setOrientation(LinearLayout.VERTICAL);
            headerContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                LinearLayout.LayoutParams.MATCH_PARENT,
                                                LinearLayout.LayoutParams.WRAP_CONTENT
                                            ));
            headerContainer.setGravity(Gravity.CENTER);

            // Nền header với gradient
            GradientDrawable headerBg = new GradientDrawable(
                GradientDrawable.Orientation.LEFT_RIGHT,
                new int[] {
                    Color.parseColor("#2196F3"), // Màu xanh dương đậm
                    Color.parseColor("#03A9F4")  // Màu xanh dương nhạt
                }
            );
            headerBg.setCornerRadii(new float[] {
                                        24 * density, 24 * density,  // top-left
                                        24 * density, 24 * density,  // top-right
                                        0, 0,                        // bottom-right
                                        0, 0                         // bottom-left
                                    });
            headerContainer.setBackground(headerBg);
            headerContainer.setPadding(0, (int)(16 * density), 0, (int)(16 * density));

            // Tiêu đề
            TextView titleText = new TextView(context);
            titleText.setText("Đang Tải MOD");
            titleText.setTextSize(18); // Giảm kích thước chữ
            titleText.setTypeface(Typeface.DEFAULT_BOLD);
            titleText.setTextColor(Color.WHITE);
            titleText.setGravity(Gravity.CENTER);
            headerContainer.addView(titleText);

            mainContainer.addView(headerContainer);

            // Content container
            LinearLayout contentContainer = new LinearLayout(context);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                 LinearLayout.LayoutParams.MATCH_PARENT,
                                                 LinearLayout.LayoutParams.WRAP_CONTENT
                                             ));
            contentContainer.setGravity(Gravity.CENTER);
            contentContainer.setPadding(
                (int)(20 * density),
                (int)(24 * density),
                (int)(20 * density),
                (int)(24 * density)
            );

            // Phần trăm tiến trình
            progressText = new TextView(context);
            progressText.setText("20%");
            progressText.setTextSize(36); // Giảm kích thước chữ
            progressText.setTypeface(Typeface.DEFAULT_BOLD);
            progressText.setTextColor(Color.parseColor("#2196F3"));
            progressText.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams progressParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            progressParams.bottomMargin = (int)(8 * density); // Thêm margin dưới
            progressText.setLayoutParams(progressParams);
            contentContainer.addView(progressText);

            // Thêm thanh tiến trình tùy chỉnh
            FrameLayout progressBarContainer = new FrameLayout(context);
            LinearLayout.LayoutParams progressBarContainerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                (int)(8 * density) // Giảm chiều cao thanh tiến trình
            );
            progressBarContainerParams.topMargin = (int)(8 * density); // Giảm margin trên
            progressBarContainerParams.bottomMargin = (int)(16 * density); // Giảm margin dưới
            progressBarContainer.setLayoutParams(progressBarContainerParams);

            // Background của thanh tiến trình
            View progressBackground = new View(context);
            progressBackground.setLayoutParams(new FrameLayout.LayoutParams(
                                                   FrameLayout.LayoutParams.MATCH_PARENT,
                                                   FrameLayout.LayoutParams.MATCH_PARENT
                                               ));
            GradientDrawable progressBgDrawable = new GradientDrawable();
            progressBgDrawable.setColor(Color.parseColor(isDarkMode ? "#3C3C3C" : "#E0E0E0"));
            progressBgDrawable.setCornerRadius(4 * density); // Giảm bo góc
            progressBackground.setBackground(progressBgDrawable);
            progressBarContainer.addView(progressBackground);

            // Foreground của thanh tiến trình (phần đã tải)
            final View progressForeground = new View(context);
            final FrameLayout.LayoutParams progressFgParams = new FrameLayout.LayoutParams(
                (int)(cardWidth * 0.2 - 40 * density), // 20% của chiều rộng (trừ padding)
                FrameLayout.LayoutParams.MATCH_PARENT
            );
            progressForeground.setLayoutParams(progressFgParams);

            // Gradient cho thanh tiến trình
            GradientDrawable progressFgDrawable = new GradientDrawable(
                GradientDrawable.Orientation.LEFT_RIGHT,
                new int[] {
                    Color.parseColor("#2196F3"), // Màu xanh dương đậm
                    Color.parseColor("#03A9F4")  // Màu xanh dương nhạt
                }
            );
            progressFgDrawable.setCornerRadius(4 * density); // Giảm bo góc
            progressForeground.setBackground(progressFgDrawable);
            progressBarContainer.addView(progressForeground);

            contentContainer.addView(progressBarContainer);

            // Container cho các chấm nhảy
            LinearLayout dotsContainer = new LinearLayout(context);
            dotsContainer.setOrientation(LinearLayout.HORIZONTAL);
            dotsContainer.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams dotsParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            dotsParams.bottomMargin = (int)(12 * density); // Giảm margin dưới
            dotsContainer.setLayoutParams(dotsParams);

            // Tạo các chấm nhảy
            final TextView[] dots = new TextView[3];
            for (int i = 0; i < 3; i++) {
                TextView dot = new TextView(context);
                dot.setText("•");
                dot.setTextSize(24); // Giảm kích thước chấm
                dot.setTextColor(Color.parseColor("#2196F3"));
                dot.setAlpha(i == 0 ? 1.0f : 0.4f); // Chấm đầu tiên sáng hơn

                LinearLayout.LayoutParams dotParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                );
                dotParams.leftMargin = (int)(6 * density); // Giảm margin
                dotParams.rightMargin = (int)(6 * density); // Giảm margin
                dot.setLayoutParams(dotParams);

                dotsContainer.addView(dot);
                dots[i] = dot;
            }
            contentContainer.addView(dotsContainer);

            // Khởi động hiệu ứng cho các chấm - Track handler để cleanup
            final Handler dotAnimHandler = new Handler(Looper.getMainLooper());
            activeHandlers.add(dotAnimHandler); // Track để cleanup
            final int[] dotIndex = {0}; // Chỉ số của chấm đang sáng

            dotAnimHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (currentDialog == null || !currentDialog.isShowing()) {
                                // Cleanup handler khi dialog đóng
                                dotAnimHandler.removeCallbacksAndMessages(null);
                                return;
                            }

                            // Làm mờ tất cả các chấm
                            for (int i = 0; i < 3; i++) {
                                dots[i].setAlpha(0.4f);
                            }

                            // Làm sáng chấm hiện tại
                            dots[dotIndex[0]].setAlpha(1.0f);

                            // Chuyển sang chấm tiếp theo
                            dotIndex[0] = (dotIndex[0] + 1) % 3;

                            // Tăng interval từ 400ms lên 800ms để giảm tải
                            dotAnimHandler.postDelayed(this, 800); // Tăng từ 400 lên 800
                        } catch (Exception e) {
                            Log.e(TAG, "Dot animation error: " + e.getMessage());
                            // Dừng animation nếu có lỗi
                            dotAnimHandler.removeCallbacksAndMessages(null);
                        }
                    }
                });

            // Text trạng thái
            statusText = new TextView(context);
            statusText.setText("Đang kết nối tới server...");
            statusText.setTextSize(14); // Giảm kích thước chữ
            statusText.setTypeface(Typeface.DEFAULT_BOLD);
            statusText.setTextColor(Color.parseColor(getColorTextPrimary()));
            statusText.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams statusParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            statusParams.bottomMargin = (int)(8 * density); // Thêm margin dưới
            statusText.setLayoutParams(statusParams);
            contentContainer.addView(statusText);

            // Thay thế phần giai đoạn bằng phần thông tin
            LinearLayout infoContainer = new LinearLayout(context);
            infoContainer.setOrientation(LinearLayout.VERTICAL);
            infoContainer.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams infoParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            infoParams.topMargin = (int)(8 * density); // Giảm margin trên
            infoContainer.setLayoutParams(infoParams);

            // Tạo đường ngăn cách (ẩn đi)
            /*
             View separator = new View(context);
             separator.setLayoutParams(new LinearLayout.LayoutParams(
             (int)(cardWidth * 0.8f),
             (int)(1 * density)
             ));
             separator.setBackgroundColor(Color.parseColor(isDarkMode ? "#38383A" : "#E0E0E0"));
             infoContainer.addView(separator);
             */

            // Thêm thông tin MOD
            TextView infoText = new TextView(context);
            infoText.setText("Hệ thống đang tải các module cần thiết");
            infoText.setTextSize(12); // Giảm kích thước chữ
            infoText.setTextColor(Color.parseColor(getColorTextSecondary()));
            infoText.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            textParams.topMargin = (int)(4 * density); // Giảm margin trên
            infoText.setLayoutParams(textParams);
            infoContainer.addView(infoText);

            // Thêm thông tin thêm (ẩn đi để làm gọn giao diện)
            /*
             TextView speedText = new TextView(context);
             speedText.setText("Vui lòng đợi trong giây lát...");
             speedText.setTextSize(13);
             speedText.setTextColor(Color.parseColor(getColorTextSecondary()));
             speedText.setGravity(Gravity.CENTER);
             LinearLayout.LayoutParams speedTextParams = new LinearLayout.LayoutParams(
             LinearLayout.LayoutParams.WRAP_CONTENT,
             LinearLayout.LayoutParams.WRAP_CONTENT
             );
             speedTextParams.topMargin = (int)(8 * density);
             speedTextParams.bottomMargin = (int)(16 * density);
             speedText.setLayoutParams(speedTextParams);
             infoContainer.addView(speedText);
             */

            // Thêm phiên bản ở góc dưới phải
            TextView versionText = new TextView(context);
            String appIdentifier = "";
            try {
                appIdentifier = GetAppIdentifier();
            } catch (Exception e) {
                appIdentifier = "aovvn_2904";
            }
            versionText.setText("Version: " + appIdentifier);
            versionText.setTextSize(9); // Giảm kích thước chữ
            versionText.setTextColor(Color.parseColor(isDarkMode ? "#555555" : "#AAAAAA")); // Làm mờ hơn
            versionText.setGravity(Gravity.END);
            LinearLayout.LayoutParams versionParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            versionParams.topMargin = (int)(4 * density); // Giảm margin trên
            versionParams.bottomMargin = (int)(4 * density); // Thêm margin dưới
            versionText.setLayoutParams(versionParams);
            infoContainer.addView(versionText);

            contentContainer.addView(infoContainer);

            mainContainer.addView(contentContainer);
            overlay.addView(mainContainer);

            // Ẩn thanh tiến trình mặc định nhưng giữ để theo dõi tiến độ
            downloadBar = new ProgressBar(context, null, android.R.attr.progressBarStyleHorizontal);
            downloadBar.setMax(100);
            downloadBar.setProgress(56); // Bắt đầu với 56%
            downloadBar.setVisibility(View.GONE);
            overlay.addView(downloadBar);

            // Handler để cập nhật giao diện theo tiến trình - Track để cleanup
            final Handler progressHandler = new Handler(Looper.getMainLooper());
            activeHandlers.add(progressHandler); // Track để cleanup
            progressHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            // Kiểm tra xem dialog có còn hiển thị không
                            if (currentDialog == null || !currentDialog.isShowing()) {
                                return;
                            }

                            if (downloadBar != null) {
                                int progress = downloadBar.getProgress();

                                // Cập nhật text phần trăm
                                progressText.setText(progress + "%");

                                // Cập nhật chiều rộng của thanh tiến trình
                                if (progressFgParams != null && progressForeground != null) {
                                    int availableWidth = cardWidth - (int)(48 * density); // Trừ padding
                                    progressFgParams.width = (int)(availableWidth * progress / 100f);
                                    progressForeground.setLayoutParams(progressFgParams);
                                }

                                // Cập nhật text trạng thái dựa vào tiến trình
                                if (progress < 30) {
                                    statusText.setText("Đang kết nối tới server...");
                                } else if (progress < 70) {
                                    statusText.setText("Đang tải dữ liệu MOD...");
                                } else {
                                    statusText.setText("Đang chuẩn bị cài đặt...");
                                }
                            }

                            progressHandler.postDelayed(this, 200); // Tăng từ 50ms lên 200ms để giảm tải
                        } catch (Exception e) {
                            Log.e(TAG, "Progress update error: " + e.getMessage());
                        }
                    }
                });

            return overlay;
        } catch (Exception e) {
            Log.e(TAG, "Error creating loading overlay: " + e.getMessage());
            // Trả về một view đơn giản nếu có lỗi
            TextView errorView = new TextView(context);
            errorView.setText("Đang tải...");
            errorView.setTextSize(16);
            errorView.setGravity(Gravity.CENTER);
            errorView.setTextColor(Color.parseColor(getColorTextPrimary()));
            errorView.setLayoutParams(new ViewGroup.LayoutParams(
                                          ViewGroup.LayoutParams.MATCH_PARENT,
                                          ViewGroup.LayoutParams.MATCH_PARENT
                                      ));
            return errorView;
        }
    }

    private static TextView createModernBadge(String text, String textColor, String bgColor) {
        Context context = getContext();
        if (context == null) return null;

        TextView badge = new TextView(context);
        badge.setText(text);
        badge.setTextSize(11); // iOS smaller text
        badge.setTypeface(null, Typeface.BOLD);
        badge.setGravity(Gravity.CENTER);
        badge.setPadding(12, 4, 12, 4); // iOS uses smaller padding

        // iOS style pill shaped badges
        GradientDrawable badgeBg = new GradientDrawable();
        badgeBg.setColor(Color.parseColor(bgColor));
        badgeBg.setCornerRadius(10); // iOS rounded pill shape
        badge.setBackground(badgeBg);
        badge.setTextColor(Color.parseColor(textColor));

        return badge;
    }

    private static void checkAndCreateCacheDir() {
        try {
            Context context = getContext();
            if (context == null) return;

            File cacheDir = context.getCacheDir();
            if (!cacheDir.exists()) {
                boolean created = cacheDir.mkdirs();
                if (!created) {
                    Log.e(TAG, "Failed to create cache directory");
                    Toast.makeText(context, "Không thể tạo thư mục cache!", Toast.LENGTH_SHORT).show();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking/creating cache dir: " + e.getMessage());
        }
    }

    private static void showModSelector() {
        try {
            Context context = getContext();
            if (context == null) return;

            // Reset trạng thái khi quay lại mod selector
            MOD_SELECTED = false;
            CURRENT_MOD_VPN_CHECK_ENABLED = false;
            LIB_NAME = "";
            Log.d(TAG, "Reset mod selection state");

            // OPTIMIZED: Dừng VPN check khi quay lại màn hình chọn mod
            // VPN check sẽ được khởi động lại dựa trên server response khi chọn mod mới
            stopVpnCheck();
            Log.d(TAG, "VPN check stopped - will restart based on next mod selection from server");

            View loadingView = createLoadingOverlay();
            if (loadingView == null) {
                showError(context, "Không thể tạo giao diện tải!");
                return;
            }

            final AlertDialog loadingDialog = new AlertDialog.Builder(context)
                .setView(loadingView)
                .setCancelable(false)
                .create();

            if (loadingDialog.getWindow() != null) {
                loadingDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            }

            try {
                loadingDialog.show();
                currentDialog = loadingDialog;
            } catch (Exception e) {
                Log.e(TAG, "Error showing dialog: " + e.getMessage());
                // Thử cách khác nếu không thể hiển thị dialog
                Activity activity = getActivity();
                if (activity != null && !activity.isFinishing()) {
                    Toast.makeText(activity, "Đang tải dữ liệu...", Toast.LENGTH_LONG).show();
                }
            }

            checkModStatus(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            if (loadingDialog != null && loadingDialog.isShowing()) {
                                loadingDialog.dismiss();
                            }
                            showModSelectorUI();
                        } catch (Exception e) {
                            Log.e(TAG, "Error in checkModStatus callback: " + e.getMessage());

                            Context ctx = getContext();
                            if (ctx != null) {
                                showError(ctx, "Lỗi khi tải dữ liệu!");
                            }

                            // Dừng animation để tránh memory leak
                            stopAllAnimations();
                        }
                    }
                });
        } catch (Exception e) {
            Log.e(TAG, "Error in showModSelector: " + e.getMessage());

            Context ctx = getContext();
            if (ctx != null) {
                showError(ctx, "Lỗi: " + e.getMessage());
            }
        }
    }

    // Phương thức showModSelectorUI() với cách bố trí mới nhất
    private static void showModSelectorUI() {
        try {
            Activity activity = getActivity();
            if (activity == null || activity.isFinishing()) {
                Log.e(TAG, "Activity is null or finishing in showModSelectorUI");
                return;
            }

            DisplayMetrics metrics = new DisplayMetrics();
            activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);
            int screenWidth = metrics.widthPixels;
            int screenHeight = metrics.heightPixels;
            float density = metrics.density;

            // Root container - nơi sẽ chứa tất cả UI
            FrameLayout rootLayout = new FrameLayout(activity);
            rootLayout.setLayoutParams(new FrameLayout.LayoutParams(
                                           FrameLayout.LayoutParams.MATCH_PARENT,
                                           FrameLayout.LayoutParams.MATCH_PARENT
                                       ));
            rootLayout.setBackgroundColor(Color.parseColor("#99000000"));

            // Main dialog container
            LinearLayout mainContainer = new LinearLayout(activity);
            mainContainer.setOrientation(LinearLayout.VERTICAL);
            mainContainer.setGravity(Gravity.CENTER_HORIZONTAL);

            LinearLayout.LayoutParams mainContainerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                (int)(screenHeight * 0.85)
            );
            int marginSize = (int)(16 * density);
            mainContainerParams.setMargins(marginSize, marginSize, marginSize, marginSize);
            mainContainerParams.gravity = Gravity.CENTER;
            mainContainer.setLayoutParams(mainContainerParams);

            // iOS style container with rounded corners
            GradientDrawable mainBg = new GradientDrawable();
            mainBg.setColor(Color.parseColor(getColorGrayBackground()));
            mainBg.setCornerRadius(16 * density);
            mainBg.setStroke(0, Color.TRANSPARENT);
            mainContainer.setBackground(mainBg);

            int horizontalPadding = (int)(16 * density);
            int verticalPadding = (int)(16 * density);
            mainContainer.setPadding(horizontalPadding, verticalPadding, horizontalPadding, verticalPadding);

            // Header layout với title ở giữa
            LinearLayout headerLayout = new LinearLayout(activity);
            headerLayout.setOrientation(LinearLayout.VERTICAL);
            headerLayout.setLayoutParams(new LinearLayout.LayoutParams(
                                             LinearLayout.LayoutParams.MATCH_PARENT,
                                             LinearLayout.LayoutParams.WRAP_CONTENT
                                         ));
            headerLayout.setGravity(Gravity.CENTER);

            // Tiêu đề chính
            TextView titleText = new TextView(activity);
            titleText.setText("Chọn Phiên Bản MOD");
            titleText.setTextSize(20);
            titleText.setTypeface(null, Typeface.BOLD);
            titleText.setTextColor(Color.parseColor(getColorTextPrimary()));
            titleText.setGravity(Gravity.CENTER);
            titleText.setLayoutParams(new LinearLayout.LayoutParams(
                                          LinearLayout.LayoutParams.WRAP_CONTENT,
                                          LinearLayout.LayoutParams.WRAP_CONTENT
                                      ));
            headerLayout.addView(titleText);

            // Thêm separator
            View separator = new View(activity);
            separator.setBackgroundColor(Color.parseColor(getColorSeparator()));
            LinearLayout.LayoutParams separatorParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                1 // iOS separator height
            );
            separatorParams.setMargins(0, (int)(16 * density), 0, (int)(8 * density));
            separator.setLayoutParams(separatorParams);
            headerLayout.addView(separator);

            mainContainer.addView(headerLayout);

            // Phần chính chứa danh sách MOD
            ScrollView gridScrollView = new ScrollView(activity);
            LinearLayout.LayoutParams scrollParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            );
            scrollParams.setMargins(0, (int)(8 * density), 0, 0);
            gridScrollView.setLayoutParams(scrollParams);
            gridScrollView.setFillViewport(true);
            gridScrollView.setOverScrollMode(View.OVER_SCROLL_NEVER);

            LinearLayout gridContainer = new LinearLayout(activity);
            gridContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                              LinearLayout.LayoutParams.MATCH_PARENT,
                                              LinearLayout.LayoutParams.WRAP_CONTENT
                                          ));
            gridContainer.setGravity(Gravity.CENTER);

            GridLayout gridLayout = new GridLayout(activity);
            gridLayout.setUseDefaultMargins(true);
            gridLayout.setAlignmentMode(GridLayout.ALIGN_BOUNDS);

            List<String> modList = new ArrayList<>(modStatuses.keySet());
            int modCount = modList.size();

            int cardSpacing = (int)(10 * density);
            int orientation = activity.getResources().getConfiguration().orientation;
            int numColumns;
            int fixedCardWidth;

            if (orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
                fixedCardWidth = (int)(220 * density);
                if (modCount <= 3) {
                    numColumns = modCount;
                } else {
                    numColumns = 2;
                }
            } else {
                numColumns = 1;
                fixedCardWidth = screenWidth - (horizontalPadding * 2) - (marginSize * 2) - (cardSpacing * 2);
            }

            gridLayout.setColumnCount(numColumns);
            gridLayout.setUseDefaultMargins(true);
            gridLayout.setAlignmentMode(GridLayout.ALIGN_BOUNDS);
            gridLayout.setColumnOrderPreserved(false);
            gridLayout.setRowOrderPreserved(false);

            for (int i = 0; i < modList.size(); i++) {
                String libName = modList.get(i);
                ModStatus status = modStatuses.get(libName);
                if (status == null) continue;

                String[] colors = getColorForIndex(i);
                LinearLayout card = createModCard(status, fixedCardWidth, colors, libName);
                if (card == null) continue;

                GridLayout.LayoutParams params = new GridLayout.LayoutParams();
                params.width = fixedCardWidth;
                params.height = GridLayout.LayoutParams.WRAP_CONTENT;
                params.setMargins(cardSpacing / 2, cardSpacing / 2, cardSpacing / 2, cardSpacing / 2);
                params.rowSpec = GridLayout.spec(i / numColumns);
                params.columnSpec = GridLayout.spec(i % numColumns);
                params.setGravity(Gravity.CENTER);

                card.setLayoutParams(params);
                gridLayout.addView(card);
            }

            gridContainer.addView(gridLayout);
            gridScrollView.addView(gridContainer);
            mainContainer.addView(gridScrollView);

            // Thêm main container vào root layout
            rootLayout.addView(mainContainer);

            // Tạo nút chuyển đổi chế độ tối/sáng LÀM NỔI BẬT bên ngoài dialog chính
            // Nút này sẽ nằm ở góc trên cùng bên phải của màn hình
            createFloatingThemeToggle(rootLayout, density);

            // Hiển thị APP_IDENTIFIER ở góc dưới bên phải
            addAppIdentifierLabel(rootLayout, density);

            Context context = getContext();
            if (context == null) return;

            try {
                AlertDialog.Builder builder = new AlertDialog.Builder(context);
                AlertDialog dialog = builder.create();
                currentDialog = dialog;

                if (dialog.getWindow() != null) {
                    dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                    try {
                        dialog.getWindow().getDecorView().setSystemUiVisibility(
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                            View.SYSTEM_UI_FLAG_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        );
                    } catch (Exception e) {
                        Log.e(TAG, "Error setting system UI visibility: " + e.getMessage());
                    }

                    WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
                    params.width = WindowManager.LayoutParams.MATCH_PARENT;
                    params.height = WindowManager.LayoutParams.MATCH_PARENT;
                    params.gravity = Gravity.CENTER;
                    params.windowAnimations = android.R.style.Animation_Dialog;
                    dialog.getWindow().setAttributes(params);
                }

                try {
                    currentDialog.setView(rootLayout);
                    currentDialog.setCancelable(false);
                    currentDialog.show();
                    isDialogShowing = true;
                } catch (Exception e) {
                    Log.e(TAG, "Error showing mod selector dialog: " + e.getMessage());
                    showError(context, "Không thể hiển thị danh sách MOD!");
                }
            } catch (Exception e) {
                Log.e(TAG, "Error in showModSelectorUI: " + e.getMessage());
                Context ctx = getContext();
                if (ctx != null) {
                    showError(ctx, "Lỗi hiển thị giao diện: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showModSelectorUI root: " + e.getMessage());
            Context ctx = getContext();
            if (ctx != null) {
                showError(ctx, "Lỗi hiển thị giao diện: " + e.getMessage());
            }
        }
    }

// Tạo nút chuyển đổi chế độ nổi
    private static void createFloatingThemeToggle(FrameLayout rootLayout, float density) {
        Context context = getContext();
        if (context == null) return;

        try {
            // Tạo nút nổi tròn để chuyển đổi chế độ
            FrameLayout toggleButton = new FrameLayout(context);
            int buttonSize = (int)(48 * density);

            FrameLayout.LayoutParams buttonParams = new FrameLayout.LayoutParams(
                buttonSize, buttonSize
            );
            buttonParams.gravity = Gravity.TOP | Gravity.RIGHT;
            buttonParams.topMargin = (int)(36 * density);
            buttonParams.rightMargin = (int)(24 * density);
            toggleButton.setLayoutParams(buttonParams);

            // Tạo hình nền tròn
            GradientDrawable buttonBg = new GradientDrawable();
            buttonBg.setShape(GradientDrawable.OVAL);
            buttonBg.setColor(Color.parseColor(isDarkMode ? "#2C2C2E" : "#FFFFFF"));
            buttonBg.setStroke((int)(1 * density), Color.parseColor(isDarkMode ? "#38383A" : "#E0E0E0"));
            toggleButton.setBackground(buttonBg);

            // Đổ bóng cho nút
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                toggleButton.setElevation(4 * density);
            }

            // Biểu tượng chế độ
            final TextView themeIcon = new TextView(context);
            themeIcon.setText(isDarkMode ? "🌙" : "☀️");
            themeIcon.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
            themeIcon.setGravity(Gravity.CENTER);

            FrameLayout.LayoutParams iconParams = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            );
            iconParams.gravity = Gravity.CENTER;
            themeIcon.setLayoutParams(iconParams);

            toggleButton.addView(themeIcon);

            // Xử lý sự kiện khi click
            toggleButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // Đảo trạng thái chế độ
                        isDarkMode = !isDarkMode;
                        saveSettings();

                        // Cập nhật icon
                        themeIcon.setText(isDarkMode ? "🌙" : "☀️");

                        // Cập nhật lại toàn bộ giao diện
                        if (currentDialog != null && currentDialog.isShowing()) {
                            currentDialog.dismiss();
                            new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        showModSelector();
                                    }
                                }, 100); // Đợi một chút để đảm bảo dialog hiện tại đã đóng hoàn toàn
                        }
                    }
                });

            // Thêm ripple effect (hiệu ứng gợn sóng khi chạm) cho nút
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                TypedValue outValue = new TypedValue();
                context.getTheme().resolveAttribute(android.R.attr.selectableItemBackgroundBorderless, outValue, true);
                toggleButton.setForeground(context.getDrawable(outValue.resourceId));
            }

            rootLayout.addView(toggleButton);
        } catch (Exception e) {
            Log.e(TAG, "Error creating floating theme toggle: " + e.getMessage());
        }
    }

    // Thêm chức năng chuyển đổi chế độ sáng/tối
    private static void addThemeToggle(LinearLayout container) {
        Context context = getContext();
        if (context == null) return;

        try {
            // Tạo container cho nút chuyển đổi ở góc trái dưới
            LinearLayout toggleContainer = new LinearLayout(context);
            toggleContainer.setOrientation(LinearLayout.HORIZONTAL);
            toggleContainer.setGravity(Gravity.CENTER_VERTICAL);

            LinearLayout.LayoutParams toggleContainerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            toggleContainerParams.setMargins((int)(16 * context.getResources().getDisplayMetrics().density),
                                             (int)(16 * context.getResources().getDisplayMetrics().density), 0, 0);
            toggleContainerParams.gravity = Gravity.BOTTOM | Gravity.LEFT;
            toggleContainer.setLayoutParams(toggleContainerParams);

            // Nền cho container toggle
            GradientDrawable toggleBg = new GradientDrawable();
            toggleBg.setColor(Color.parseColor(isDarkMode ? "#2C2C2E" : "#F5F5F5"));
            toggleBg.setCornerRadius(15 * context.getResources().getDisplayMetrics().density);
            toggleBg.setStroke(1, Color.parseColor(isDarkMode ? "#38383A" : "#E0E0E0"));
            toggleContainer.setBackground(toggleBg);
            toggleContainer.setPadding(
                (int)(12 * context.getResources().getDisplayMetrics().density),
                (int)(8 * context.getResources().getDisplayMetrics().density),
                (int)(12 * context.getResources().getDisplayMetrics().density),
                (int)(8 * context.getResources().getDisplayMetrics().density)
            );

            // Nhãn "Chế độ tối"
            TextView themeLabel = new TextView(context);
            themeLabel.setText("Chế độ tối");
            themeLabel.setTextSize(12);
            themeLabel.setTextColor(Color.parseColor(getColorTextSecondary()));
            themeLabel.setPadding(0, 0, (int)(10 * context.getResources().getDisplayMetrics().density), 0);
            toggleContainer.addView(themeLabel);

            // Switch toggle
            final Switch themeSwitch = new Switch(context);
            themeSwitch.setChecked(isDarkMode);

            // Tùy chỉnh màu sắc của Switch
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
                themeSwitch.setThumbTextPadding((int)(8 * context.getResources().getDisplayMetrics().density));
            }

            // Xử lý sự kiện khi toggle chế độ
            themeSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        isDarkMode = isChecked;
                        saveSettings();
                        // Cập nhật lại giao diện
                        if (currentDialog != null && currentDialog.isShowing()) {
                            currentDialog.dismiss();
                            showModSelector();
                        }
                    }
                });

            toggleContainer.addView(themeSwitch);
            container.addView(toggleContainer);
        } catch (Exception e) {
            Log.e(TAG, "Error adding theme toggle: " + e.getMessage());
        }
    }

    private static String[] getColorForIndex(int index) {
        // iOS style colors - đảm bảo an toàn
        try {
            String[][] colors = {
                {getColorBlueLight(), getColorBlue()},            // iOS blue
                {getColorGreenLight(), getColorGreen()},          // iOS green
                {getColorOrangeLight(), getColorOrange()},        // iOS orange
                {getColorPurple() + (isDarkMode ? "30" : "20"), getColorPurple()},  // iOS purple
                {isDarkMode ? "#2C2C2E30" : "#F2F2F720", getColorTextSecondary()},  // iOS gray
                {isDarkMode ? "#1B3A57" : "#E9F7FD", isDarkMode ? "#64D2FF" : "#5AC8FA"},  // iOS light blue
                {isDarkMode ? "#3D2911" : "#FDF2E9", getColorOrange()},             // iOS orange
                {isDarkMode ? "#3B1F32" : "#FBE9F1", isDarkMode ? "#FF375F" : "#FF2D55"}  // iOS pink
            };

            if (index < 0) index = 0;
            return colors[index % colors.length];
        } catch (Exception e) {
            Log.e(TAG, "Error in getColorForIndex: " + e.getMessage());
            // Trả về màu mặc định nếu có lỗi
            return new String[] {getColorBlueLight(), getColorBlue()};
        }
    }

    private static LinearLayout createModCard(final ModStatus status, int width, String[] colors, final String libName) {
        Context context = getContext();
        if (context == null) return null;

        try {
            LinearLayout card = new LinearLayout(context);
            card.setOrientation(LinearLayout.VERTICAL);
            card.setGravity(Gravity.CENTER_HORIZONTAL);

            LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(width, LinearLayout.LayoutParams.WRAP_CONTENT);
            cardParams.setMargins(10, 10, 10, 10);
            card.setLayoutParams(cardParams);

            // iOS style card with rounded corners
            GradientDrawable cardBg = new GradientDrawable();
            cardBg.setColor(Color.parseColor(getColorCardBackground()));
            cardBg.setCornerRadius(14); // iOS uses consistent corner radius

// Thêm viền mỏng cho card khi ở chế độ tối
            if (isDarkMode) {
                cardBg.setStroke(1, Color.parseColor("#38383A")); // Viền mỏng cho chế độ tối
            } else {
                cardBg.setStroke(0, Color.TRANSPARENT); // Không viền cho chế độ sáng
            }

            card.setBackground(cardBg);
            card.setElevation(1.5f); // iOS uses subtle shadows

            // Container cho ngôi sao phía trên
            RelativeLayout starIconContainer = new RelativeLayout(context);
            starIconContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                  LinearLayout.LayoutParams.MATCH_PARENT,
                                                  LinearLayout.LayoutParams.WRAP_CONTENT
                                              ));

            TextView starIcon = new TextView(context);
            if (status != null && status.icon != null && !status.icon.isEmpty()) {
                starIcon.setText(status.icon); // Sử dụng biểu tượng từ JSON
            } else {
                starIcon.setText("★"); // Sử dụng emoji mặc định nếu không có dữ liệu từ server
            }
            starIcon.setTextSize(24);
            starIcon.setTextColor(Color.parseColor("#FFD700")); // Màu vàng cho ngôi sao

            RelativeLayout.LayoutParams starParams = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT
            );
            starParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
            starParams.topMargin = (int)(10 * card.getContext().getResources().getDisplayMetrics().density);
            starIcon.setLayoutParams(starParams);

            starIconContainer.addView(starIcon);
            card.addView(starIconContainer);

            LinearLayout contentContainer = new LinearLayout(context);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setGravity(Gravity.CENTER_HORIZONTAL);
            contentContainer.setPadding(16, 5, 16, 16); // Giảm padding phía trên

            // Container cho tiêu đề và icon VPN
            LinearLayout titleContainer = new LinearLayout(context);
            titleContainer.setOrientation(LinearLayout.HORIZONTAL);
            titleContainer.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams titleContainerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            titleContainerParams.setMargins(0, 10, 0, 4);
            titleContainer.setLayoutParams(titleContainerParams);

            TextView titleText = new TextView(context);
            titleText.setText(status.title);
            titleText.setTextSize(15); // iOS text size
            titleText.setTypeface(null, Typeface.BOLD);
            titleText.setGravity(Gravity.CENTER);
            titleText.setTextColor(Color.parseColor(getColorTextPrimary()));
            titleContainer.addView(titleText);

            // Thêm icon VPN nhỏ nếu có VPN check
            if (status.vpnCheckEnabled) {
                TextView vpnIcon = new TextView(context);
                vpnIcon.setText(" 🛡️");
                vpnIcon.setTextSize(12);
                vpnIcon.setGravity(Gravity.CENTER);
                LinearLayout.LayoutParams vpnIconParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                );
                vpnIconParams.setMargins((int)(4 * context.getResources().getDisplayMetrics().density), 0, 0, 0);
                vpnIcon.setLayoutParams(vpnIconParams);
                titleContainer.addView(vpnIcon);
            }

            contentContainer.addView(titleContainer);

            TextView versionText = new TextView(context);
            versionText.setText("Phiên bản: " + status.version);
            versionText.setTextSize(11); // iOS secondary text
            versionText.setGravity(Gravity.CENTER);
            versionText.setTextColor(Color.parseColor(getColorTextSecondary()));
            versionText.setPadding(0, 2, 0, 16);
            contentContainer.addView(versionText);

            // iOS style scroll view
            ScrollView featuresScrollContainer = new ScrollView(context);
            featuresScrollContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                                        180 // iOS uses more compact layouts
                                                    ));
            featuresScrollContainer.setOverScrollMode(View.OVER_SCROLL_NEVER); // iOS scrolling style

            LinearLayout featuresContainer = new LinearLayout(context);
            featuresContainer.setOrientation(LinearLayout.VERTICAL);
            featuresContainer.setPadding(8, 4, 8, 4);

            try {
                String[] features = status.description.split("\\+");
                for (String feature : features) {
                    LinearLayout featureRow = new LinearLayout(context);
                    featureRow.setOrientation(LinearLayout.HORIZONTAL);
                    featureRow.setGravity(Gravity.CENTER_VERTICAL);
                    featureRow.setPadding(0, 5, 0, 5);

                    // iOS style checkmark bullet points
                    TextView bulletPoint = new TextView(context);
                    bulletPoint.setText("✓"); // iOS style checkmark
                    bulletPoint.setTextSize(12);
                    bulletPoint.setTextColor(Color.parseColor(colors[1]));
                    bulletPoint.setPadding(0, 0, 10, 0);
                    featureRow.addView(bulletPoint);

                    TextView featureText = new TextView(context);
                    featureText.setText(feature.trim());
                    featureText.setTextSize(12); // iOS text size
                    featureText.setTextColor(Color.parseColor(getColorTextSecondary()));
                    featureRow.addView(featureText);

                    featuresContainer.addView(featureRow);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error parsing features: " + e.getMessage());
                // Fallback nếu có lỗi
                TextView fallbackText = new TextView(context);
                fallbackText.setText("Tính năng: " + status.description);
                fallbackText.setTextSize(12);
                fallbackText.setTextColor(Color.parseColor(getColorTextSecondary()));
                fallbackText.setPadding(8, 8, 8, 8);
                featuresContainer.addView(fallbackText);
            }

            featuresScrollContainer.addView(featuresContainer);
            contentContainer.addView(featuresScrollContainer);

            // iOS style badges
            LinearLayout badgeContainer = new LinearLayout(context);
            badgeContainer.setOrientation(LinearLayout.HORIZONTAL);
            badgeContainer.setGravity(Gravity.CENTER);
            badgeContainer.setPadding(0, 12, 0, 16);

            TextView safetyBadge = createModernBadge(
                "safe".equals(status.safety) ? "✅An Toàn" : "⛔Lưu Ý", // iOS simpler labels
                "safe".equals(status.safety) ? getColorGreen() : getColorRed(),
                "safe".equals(status.safety) ? getColorGreenLight() : getColorRedLight()
            );
            if (safetyBadge != null) {
                badgeContainer.addView(safetyBadge);
            }

            View spacer = new View(context);
            spacer.setLayoutParams(new LinearLayout.LayoutParams(10, 1)); // iOS spacing
            badgeContainer.addView(spacer);

            TextView updateBadge = createModernBadge(
                "ready".equals(status.status) ? "♻️Đã Update" : "🚫Cần Update",
                "ready".equals(status.status) ? getColorBlue() : getColorOrange(),
                "ready".equals(status.status) ? getColorBlueLight() : getColorOrangeLight()
            );
            if (updateBadge != null) {
                badgeContainer.addView(updateBadge);
            }


            contentContainer.addView(badgeContainer);

            // iOS style button
            LinearLayout buttonContainer = new LinearLayout(context);
            buttonContainer.setPadding(16, 0, 16, 16);

            TextView selectButton = new TextView(context);
            selectButton.setText("Chọn");
            selectButton.setTextSize(15);
            selectButton.setTypeface(Typeface.DEFAULT_BOLD); // Sử dụng DEFAULT_BOLD thay vì SEMIBOLD
            selectButton.setTextColor(Color.WHITE);
            selectButton.setGravity(Gravity.CENTER);
            selectButton.setPadding(0, 12, 0, 12); // iOS padding

            TypedValue outValue = new TypedValue();
            context.getTheme().resolveAttribute(android.R.attr.selectableItemBackground, outValue, true);
            selectButton.setBackgroundResource(outValue.resourceId);

            selectButton.setClickable(true);
            selectButton.setFocusable(true);

            LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            selectButton.setLayoutParams(buttonParams);

            // iOS style button with rounded corners
            GradientDrawable buttonBg = new GradientDrawable();
            buttonBg.setColor(Color.parseColor(getColorBlue())); // iOS primary button color
            buttonBg.setCornerRadius(10); // iOS button corner radius
            selectButton.setBackground(buttonBg);

            selectButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (isLoading) {
                            return;
                        }
                        handleModSelection(status, libName);
                    }
                });

            buttonContainer.addView(selectButton);
            card.addView(contentContainer);
            card.addView(buttonContainer);

            return card;
        } catch (Exception e) {
            Log.e(TAG, "Error creating mod card: " + e.getMessage());
            // Trả về card đơn giản nếu có lỗi
            LinearLayout fallbackCard = new LinearLayout(context);
            fallbackCard.setOrientation(LinearLayout.VERTICAL);
            TextView fallbackText = new TextView(context);
            fallbackText.setText(status != null ? status.title : "Unknown Mod");
            fallbackText.setGravity(Gravity.CENTER);
            fallbackText.setTextColor(Color.parseColor(getColorTextPrimary()));
            fallbackText.setPadding(20, 20, 20, 20);
            fallbackCard.addView(fallbackText);
            return fallbackCard;
        }
    }

    private static void handleModSelection(ModStatus status, String libName) {
        try {
            if (status == null) {
                Context context = getContext();
                if (context != null) {
                    showError(context, "Thông tin MOD không hợp lệ!");
                }
                return;
            }

            // Set trạng thái VPN check cho mod hiện tại dựa trên server response
            MOD_SELECTED = true;
            CURRENT_MOD_VPN_CHECK_ENABLED = status.vpnCheckEnabled;
            Log.d(TAG, "Mod selected: " + libName + ", VPN check from server: " + (CURRENT_MOD_VPN_CHECK_ENABLED ? "ENABLED" : "DISABLED"));

            // 🛡️ SECURE VPN CHECK: Kiểm tra VPN ngay khi chọn mod với multiple methods
            if (CURRENT_MOD_VPN_CHECK_ENABLED && isVpnEnabledSecure()) {
                Context context = getContext();
                if (context != null) {
                    showError(context, "🚨 Phát hiện VPN! Vui lòng tắt VPN trước khi chọn MOD này!");
                    return; // Không cho phép chọn mod nếu VPN đang bật
                }
            }

            // ✅ ENHANCED: Bắt đầu continuous VPN monitoring ngay khi chọn mod (nếu mod yêu cầu)
            if (CURRENT_MOD_VPN_CHECK_ENABLED) {
                Log.d(TAG, "✅ Starting continuous VPN monitoring for selected mod: " + libName);
                startContinuousVpnCheck();
            }

            Log.d(TAG, "Mod selection allowed - Continuous VPN check " + (CURRENT_MOD_VPN_CHECK_ENABLED ? "STARTED" : "NOT REQUIRED"));

            if ("unsafe".equals(status.safety)) {
                showDangerWarning(status.title, status.description, libName, status.safetyMessage);
            } else {
                showConfirmAndLoad(currentDialog, status.title, status.description, libName);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in handleModSelection: " + e.getMessage());
            Context context = getContext();
            if (context != null) {
                showError(context, "Lỗi xử lý lựa chọn: " + e.getMessage());
            }
        }
    }

    private static void showDangerWarning(final String modName, final String description, final String libName, String warning) {
        Context context = getContext();
        if (context == null) return;

        try {
            LinearLayout warningLayout = new LinearLayout(context);
            warningLayout.setOrientation(LinearLayout.VERTICAL);
            warningLayout.setLayoutParams(new LinearLayout.LayoutParams(
                                              LinearLayout.LayoutParams.MATCH_PARENT,
                                              LinearLayout.LayoutParams.WRAP_CONTENT
                                          ));

            // iOS style alert dialog
            GradientDrawable shape = new GradientDrawable();
            shape.setColor(Color.parseColor(getColorPrimary()));
            shape.setCornerRadius(16); // iOS dialog corners
            warningLayout.setBackground(shape);
            warningLayout.setPadding(24, 24, 24, 20);

            // iOS style alert icon
            LinearLayout iconContainer = new LinearLayout(context);
            iconContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                              LinearLayout.LayoutParams.MATCH_PARENT,
                                              LinearLayout.LayoutParams.WRAP_CONTENT
                                          ));

            iconContainer.setGravity(Gravity.CENTER);
            iconContainer.setPadding(0, 0, 0, 16);

            TextView warningIcon = new TextView(context);
            warningIcon.setText("⚠️");
            warningIcon.setTextSize(32); // iOS alert icon
            warningIcon.setGravity(Gravity.CENTER);
            iconContainer.addView(warningIcon);
            warningLayout.addView(iconContainer);

            TextView titleText = new TextView(context);
            titleText.setText("Cảnh Báo!");
            titleText.setTextSize(17); // iOS title size
            titleText.setTypeface(null, Typeface.BOLD);
            titleText.setTextColor(Color.parseColor(getColorRed())); // iOS red warning
            titleText.setGravity(Gravity.CENTER);

            LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            titleParams.setMargins(0, 0, 0, 16); // iOS spacing
            titleText.setLayoutParams(titleParams);
            warningLayout.addView(titleText);

            TextView warningText = new TextView(context);
            warningText.setText(warning);
            warningText.setTextSize(14); // iOS text size
            warningText.setLineSpacing(0, 1.2f);
            warningText.setTextColor(Color.parseColor(getColorTextPrimary())); // iOS text color
            warningText.setGravity(Gravity.CENTER);

            LinearLayout.LayoutParams warningParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            warningParams.setMargins(0, 0, 0, 24);
            warningText.setLayoutParams(warningParams);
            warningLayout.addView(warningText);

            // Separator trước phần button (iOS style)
            View separator = new View(context);
            separator.setBackgroundColor(Color.parseColor(getColorSeparator()));
            LinearLayout.LayoutParams separatorParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                1 // iOS separator height
            );
            separatorParams.setMargins(0, 0, 0, 0);
            separator.setLayoutParams(separatorParams);
            warningLayout.addView(separator);

            // iOS style button container (horizontal layout for multiple buttons)
            LinearLayout buttonContainer = new LinearLayout(context);
            buttonContainer.setOrientation(LinearLayout.HORIZONTAL);
            buttonContainer.setGravity(Gravity.CENTER);
            buttonContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                LinearLayout.LayoutParams.MATCH_PARENT,
                                                LinearLayout.LayoutParams.WRAP_CONTENT
                                            ));

            // iOS style separators between buttons
            View buttonSeparator = new View(context);
            buttonSeparator.setBackgroundColor(Color.parseColor(getColorSeparator()));
            buttonSeparator.setLayoutParams(new LinearLayout.LayoutParams(
                                                1, // Width
                                                LinearLayout.LayoutParams.MATCH_PARENT
                                            ));

            // iOS style cancel button (Text only)
            TextView cancelButton = new TextView(context);
            cancelButton.setText("Hủy");
            cancelButton.setTextSize(16);
            cancelButton.setTypeface(Typeface.DEFAULT_BOLD); // Sử dụng DEFAULT_BOLD thay vì SEMIBOLD
            cancelButton.setTextColor(Color.parseColor(getColorBlue())); // iOS blue for cancel
            cancelButton.setGravity(Gravity.CENTER);
            cancelButton.setPadding(0, 12, 0, 12);

            cancelButton.setClickable(true);
            cancelButton.setFocusable(true);

            LinearLayout.LayoutParams cancelParams = new LinearLayout.LayoutParams(
                0, // Width 0 with weight makes buttons equal width
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            cancelParams.weight = 1; // Equal width buttons
            cancelParams.height = (int)(44 * context.getResources().getDisplayMetrics().density); // iOS button height
            cancelButton.setLayoutParams(cancelParams);

            // iOS style destructive button (red text)
            TextView continueButton = new TextView(context);
            continueButton.setText("Tiếp Tục");
            continueButton.setTextSize(16);
            continueButton.setTypeface(Typeface.DEFAULT_BOLD); // Sử dụng DEFAULT_BOLD thay vì SEMIBOLD
            continueButton.setTextColor(Color.parseColor(getColorRed())); // iOS destructive action
            continueButton.setGravity(Gravity.CENTER);
            continueButton.setPadding(0, 12, 0, 12);

            continueButton.setClickable(true);
            continueButton.setFocusable(true);

            LinearLayout.LayoutParams continueParams = new LinearLayout.LayoutParams(
                0, // Width 0 with weight makes buttons equal width
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            continueParams.weight = 1; // Equal width buttons
            continueParams.height = (int)(44 * context.getResources().getDisplayMetrics().density); // iOS button height
            continueButton.setLayoutParams(continueParams);

            buttonContainer.addView(cancelButton);
            buttonContainer.addView(buttonSeparator);
            buttonContainer.addView(continueButton);
            warningLayout.addView(buttonContainer);

            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            final AlertDialog dialog = builder.create();

            if (dialog != null && dialog.getWindow() != null) {
                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            }

            cancelButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            if (dialog != null && dialog.isShowing()) {
                                dialog.dismiss();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                        }
                    }
                });

            continueButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            if (dialog != null && dialog.isShowing()) {
                                dialog.dismiss();
                            }
                            showConfirmAndLoad(currentDialog, modName, description, libName);
                        } catch (Exception e) {
                            Log.e(TAG, "Error on continue: " + e.getMessage());
                        }
                    }
                });

            try {
                dialog.setView(warningLayout);
                dialog.show();
            } catch (Exception e) {
                Log.e(TAG, "Error showing danger warning dialog: " + e.getMessage());
                showError(context, "Không thể hiển thị cảnh báo");
                return;
            }

            Window window = dialog.getWindow();
            if (window != null) {
                try {
                    DisplayMetrics metrics = new DisplayMetrics();
                    Activity activity = getActivity();
                    if (activity != null) {
                        activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);

                        int width = (int)(metrics.widthPixels * 0.75); // iOS dialogs are narrower
                        WindowManager.LayoutParams params = window.getAttributes();
                        params.width = Math.min(width, 1000); // iOS consistent width dialogs
                        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
                        params.gravity = Gravity.CENTER;
                        params.dimAmount = 0.5f; // iOS dim
                        params.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
                        window.setAttributes(params);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error setting warning dialog dimensions: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showDangerWarning: " + e.getMessage());
            Context ctx = getContext();
            if (ctx != null) {
                showError(ctx, "Lỗi hiển thị cảnh báo");
            }
        }
    }

    private static void showConfirmAndLoad(final AlertDialog mainDialog, String modName, String description, final String libName) {
        Context context = getContext();
        if (context == null) return;

        try {
            LinearLayout confirmLayout = new LinearLayout(context);
            confirmLayout.setOrientation(LinearLayout.VERTICAL);
            confirmLayout.setLayoutParams(new LinearLayout.LayoutParams(
                                              LinearLayout.LayoutParams.MATCH_PARENT,
                                              LinearLayout.LayoutParams.WRAP_CONTENT
                                          ));

            // iOS style confirmation dialog
            GradientDrawable shape = new GradientDrawable();
            shape.setColor(Color.parseColor(getColorPrimary()));
            shape.setCornerRadius(16); // iOS corner radius
            confirmLayout.setBackground(shape);
            confirmLayout.setPadding(40, 40, 40, 0); // Tăng padding rất nhiều

            // iOS style title
            TextView titleText = new TextView(context);
            titleText.setText(modName);
            titleText.setTextSize(22); // Tăng font size rất lớn
            titleText.setTypeface(null, Typeface.BOLD);
            titleText.setTextColor(Color.parseColor(getColorTextPrimary()));
            titleText.setGravity(Gravity.CENTER);
            confirmLayout.addView(titleText);

            // iOS style description
            TextView descriptionText = new TextView(context);
            descriptionText.setText(description.replace("+", "\n• ")); // iOS bullet points
            descriptionText.setTextSize(17); // Tăng font size
            descriptionText.setTextColor(Color.parseColor(getColorTextSecondary()));
            descriptionText.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams descParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            descParams.setMargins(0, 24, 0, 32); // Tăng margins
            descriptionText.setLayoutParams(descParams);
            confirmLayout.addView(descriptionText);

            // iOS style confirmation text
            TextView confirmText = new TextView(context);
            confirmText.setText("Bạn muốn sử dụng bản MOD này?");
            confirmText.setTextSize(18); // Tăng font size
            confirmText.setTextColor(Color.parseColor(getColorTextPrimary()));
            confirmText.setGravity(Gravity.CENTER);
            confirmText.setPadding(0, 0, 0, 40); // Tăng padding rất nhiều
            confirmLayout.addView(confirmText);

            // Separator trước phần button
            View separator = new View(context);
            separator.setBackgroundColor(Color.parseColor(getColorSeparator()));
            LinearLayout.LayoutParams separatorParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                1 // iOS separator height
            );
            separatorParams.setMargins(0, 0, 0, 0);
            separator.setLayoutParams(separatorParams);
            confirmLayout.addView(separator);

            // iOS style button container with equal width buttons
            LinearLayout buttonContainer = new LinearLayout(context);
            buttonContainer.setOrientation(LinearLayout.HORIZONTAL);
            buttonContainer.setGravity(Gravity.CENTER);
            buttonContainer.setLayoutParams(new LinearLayout.LayoutParams(
                                                LinearLayout.LayoutParams.MATCH_PARENT,
                                                LinearLayout.LayoutParams.WRAP_CONTENT
                                            ));

            // iOS style separator between buttons
            View buttonSeparator = new View(context);
            buttonSeparator.setBackgroundColor(Color.parseColor(getColorSeparator()));
            buttonSeparator.setLayoutParams(new LinearLayout.LayoutParams(
                                                1, // Width
                                                LinearLayout.LayoutParams.MATCH_PARENT
                                            ));

            // iOS style cancel button
            final TextView cancelButton = new TextView(context);
            cancelButton.setText("Hủy");
            cancelButton.setTextSize(18); // Tăng font size
            cancelButton.setTypeface(Typeface.DEFAULT_BOLD);
            cancelButton.setTextColor(Color.parseColor(getColorBlue())); // iOS blue
            cancelButton.setGravity(Gravity.CENTER);
            cancelButton.setPadding(0, 20, 0, 20); // Tăng padding rất nhiều
            cancelButton.setClickable(true);
            cancelButton.setFocusable(true);

            LinearLayout.LayoutParams cancelParams = new LinearLayout.LayoutParams(
                0, // Width 0 with weight for equal width buttons
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            cancelParams.weight = 1;
            cancelParams.height = (int)(60 * context.getResources().getDisplayMetrics().density); // Tăng chiều cao nút
            cancelButton.setLayoutParams(cancelParams);

            // iOS style confirm button
            TextView confirmButton = new TextView(context);
            confirmButton.setText("Xác nhận");
            confirmButton.setTextSize(18); // Tăng font size
            confirmButton.setTypeface(Typeface.DEFAULT_BOLD);
            confirmButton.setTextColor(Color.parseColor(getColorBlue())); // iOS blue (bolder for primary action)
            confirmButton.setGravity(Gravity.CENTER);
            confirmButton.setPadding(0, 20, 0, 20); // Tăng padding rất nhiều
            confirmButton.setClickable(true);
            confirmButton.setFocusable(true);

            LinearLayout.LayoutParams confirmParams = new LinearLayout.LayoutParams(
                0, // Width 0 with weight for equal width buttons
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            confirmParams.weight = 1;
            confirmParams.height = (int)(60 * context.getResources().getDisplayMetrics().density); // Tăng chiều cao nút
            confirmButton.setLayoutParams(confirmParams);

            buttonContainer.addView(cancelButton);
            buttonContainer.addView(buttonSeparator);
            buttonContainer.addView(confirmButton);
            confirmLayout.addView(buttonContainer);

            FrameLayout rootContainer = new FrameLayout(context);
            rootContainer.addView(confirmLayout);

            final View blockingOverlay = new View(context);
            blockingOverlay.setLayoutParams(new FrameLayout.LayoutParams(
                                                FrameLayout.LayoutParams.MATCH_PARENT,
                                                FrameLayout.LayoutParams.MATCH_PARENT
                                            ));
            blockingOverlay.setBackgroundColor(Color.TRANSPARENT);
            blockingOverlay.setVisibility(View.GONE);
            blockingOverlay.setClickable(true);
            blockingOverlay.setFocusable(true);
            rootContainer.addView(blockingOverlay);

            AlertDialog.Builder builder = new AlertDialog.Builder(context);
            final AlertDialog dialog = builder.create();

            if (dialog != null && dialog.getWindow() != null) {
                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            }

            cancelButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            if (dialog != null && dialog.isShowing()) {
                                dialog.dismiss();
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                        }
                    }
                });

            confirmButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            showGlobalBlockingOverlay();
                            v.setEnabled(false);
                            cancelButton.setEnabled(false);
                            blockingOverlay.setVisibility(View.VISIBLE);

                            LIB_NAME = libName;
                            if (dialog != null && dialog.isShowing()) {
                                dialog.dismiss();
                            }

                            if (mainDialog != null && mainDialog.getWindow() != null) {
                                View loadingOverlay = createLoadingOverlay();
                                if (loadingOverlay != null) {
                                    ((ViewGroup)mainDialog.getWindow().getDecorView()).addView(loadingOverlay);
                                }
                            }

                            blockingOverlay.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        // Block all interactions
                                    }
                                });

                            loadSelectedMod(mainDialog);
                        } catch (Exception e) {
                            Log.e(TAG, "Error confirming mod selection: " + e.getMessage());
                            Context ctx = getContext();
                            if (ctx != null) {
                                showError(ctx, "Lỗi khi xác nhận: " + e.getMessage());
                            }
                            hideGlobalBlockingOverlay();
                        }
                    }
                });

            try {
                dialog.setView(rootContainer);
                dialog.show();
            } catch (Exception e) {
                Log.e(TAG, "Error showing confirm dialog: " + e.getMessage());
                showError(context, "Không thể hiển thị xác nhận");
                return;
            }

            Window window = dialog.getWindow();
            if (window != null) {
                try {
                    DisplayMetrics metrics = new DisplayMetrics();
                    Activity activity = getActivity();
                    if (activity != null) {
                        activity.getWindowManager().getDefaultDisplay().getMetrics(metrics);

                        // Tăng kích thước lên 70% chiều rộng màn hình
                        int width = (int)(metrics.widthPixels * 0.70);
                        WindowManager.LayoutParams params = window.getAttributes();
                        params.width = width;
                        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
                        params.gravity = Gravity.CENTER;
                        params.dimAmount = 0.45f;
                        params.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
                        window.setAttributes(params);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error setting confirm dialog dimensions: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in showConfirmAndLoad: " + e.getMessage());
            showError(context, "Lỗi hiển thị hộp thoại xác nhận");
        }
    }

    private static void loadSelectedMod(final AlertDialog dialog) {
        final Context context = getContext();
        if (context == null) return;

        // OPTIMIZED: Kiểm tra VPN và bắt đầu VPN monitoring chỉ khi cần tải MOD
        if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED) {
            // Kiểm tra VPN trước khi tải
            if (isVpnEnabled()) {
                hideGlobalBlockingOverlay();
                stopAllAnimations();
                showError(context, "Vui lòng tắt VPN trước khi tải MOD!");

                // Đóng dialog sau 2 giây và quay lại màn hình chọn MOD
                Handler delayHandler = new Handler();
                activeHandlers.add(delayHandler);
                delayHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (dialog != null && dialog.isShowing()) {
                                dialog.dismiss();
                            }
                            showModSelector();
                        }
                    }, 2000);

                return;
            }

            // Bắt đầu VPN check chỉ khi bắt đầu tải mod
            Log.d(TAG, "Starting VPN check during mod download as required by server");
            startVpnCheck();
        } else if (MOD_SELECTED && !CURRENT_MOD_VPN_CHECK_ENABLED && isVpnEnabled()) {
            Log.d(TAG, "VPN detected but allowed for current mod, proceeding with download");
        }

        // Kiểm tra LIB_NAME đã được set chưa
        if (LIB_NAME == null || LIB_NAME.trim().isEmpty()) {
            hideGlobalBlockingOverlay();
            stopAllAnimations();
            showError(context, "Lỗi: Tên thư viện không hợp lệ!");

            Handler errorHandler = new Handler();
            activeHandlers.add(errorHandler);
            errorHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                        showModSelector();
                    }
                }, 2000);
            return;
        }

        final String libPath = context.getCacheDir() + "/" + LIB_NAME;

        checkAndCreateCacheDir();

        // Ghi đè trực tiếp lên file cũ - đơn giản và an toàn nhất

        final Thread downloadThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        // Kiểm tra thread có bị interrupt không
                        if (Thread.currentThread().isInterrupted()) {
                            return;
                        }

                        // Khởi tạo tiến trình tải xuống
                        downloadProgress = 0;

                        // Cập nhật UI ban đầu
                        mainHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        if (progressText != null) {
                                            progressText.setText("0%");
                                        }
                                        if (downloadBar != null) {
                                            downloadBar.setProgress(0);
                                        }
                                        if (statusText != null) {
                                            statusText.setText("Đang kết nối server...");
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error updating initial UI: " + e.getMessage());
                                    }
                                }
                            });

                        // Tạo một đối tượng để theo dõi thời gian tải
                        final long startTime = System.currentTimeMillis();
                        final long[] lastUpdateTime = {startTime};
                        final int[] lastProgress = {0};

                        // Tạo một thread riêng để cập nhật UI trong khi đang tải
                        final Thread progressUpdateThread = new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        // Biến để theo dõi thời gian kiểm tra VPN
                                        final long[] lastVpnCheckTime = {System.currentTimeMillis()};

                                        while (downloadProgress < 100 && !Thread.currentThread().isInterrupted()) {
                                            // Kiểm tra thread chính có bị interrupt không
                                            if (Thread.currentThread().isInterrupted()) {
                                                break;
                                            }
                                            // Cập nhật UI mỗi 100ms
                                            mainHandler.post(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        try {
                                                            if (progressText != null) {
                                                                progressText.setText(downloadProgress + "%");
                                                            }
                                                            if (downloadBar != null) {
                                                                downloadBar.setProgress(downloadProgress);
                                                            }

                                                            // Cập nhật trạng thái dựa trên tiến trình
                                                            if (statusText != null) {
                                                                if (downloadProgress < 20) {
                                                                    statusText.setText("Đang kết nối server...");
                                                                } else if (downloadProgress < 50) {
                                                                    statusText.setText("Đang tải dữ liệu MOD...");
                                                                } else if (downloadProgress < 80) {
                                                                    statusText.setText("Đang giải mã dữ liệu...");
                                                                } else if (downloadProgress < 95) {
                                                                    statusText.setText("Đang chuẩn bị cài đặt...");
                                                                } else {
                                                                    statusText.setText("Hoàn tất cài đặt...");
                                                                }
                                                            }

                                                            // Tính toán tốc độ tải
                                                            long currentTime = System.currentTimeMillis();
                                                            if (currentTime - lastUpdateTime[0] >= 1000) { // Cập nhật mỗi giây
                                                                int progressDiff = downloadProgress - lastProgress[0];
                                                                lastProgress[0] = downloadProgress;
                                                                lastUpdateTime[0] = currentTime;

                                                                // Hiển thị tốc độ tải nếu có sự thay đổi
                                                                if (progressDiff > 0) {
                                                                    Log.d(TAG, "Download speed: " + progressDiff + "% per second");
                                                                }
                                                            }

                                                            // Kiểm tra VPN mỗi 5 giây (chỉ khi đã chọn mod và mod yêu cầu)
                                                            if (currentTime - lastVpnCheckTime[0] >= 5000) {
                                                                lastVpnCheckTime[0] = currentTime;

                                                                if (MOD_SELECTED && CURRENT_MOD_VPN_CHECK_ENABLED && isVpnEnabled()) {
                                                                    // Phát hiện VPN được bật trong quá trình tải
                                                                    Log.e(TAG, "VPN detected during download (VPN check enabled for current mod)");

                                                                    // Dừng tải và hiển thị thông báo
                                                                    mainHandler.post(new Runnable() {
                                                                            @Override
                                                                            public void run() {
                                                                                hideGlobalBlockingOverlay();
                                                                                stopAllAnimations();
                                                                                showError(context, "Phát hiện VPN đang bật! Quá trình tải bị hủy.");

                                                                                // Đóng dialog sau 2 giây và quay lại màn hình chọn MOD
                                                                                Handler vpnHandler = new Handler();
                                                                                activeHandlers.add(vpnHandler);
                                                                                vpnHandler.postDelayed(new Runnable() {
                                                                                        @Override
                                                                                        public void run() {
                                                                                            if (dialog != null && dialog.isShowing()) {
                                                                                                dialog.dismiss();
                                                                                            }
                                                                                            showModSelector();
                                                                                        }
                                                                                    }, 2000);
                                                                            }
                                                                        });

                                                                    // Dừng thread cập nhật
                                                                    return;
                                                                } else if (MOD_SELECTED && !CURRENT_MOD_VPN_CHECK_ENABLED && isVpnEnabled()) {
                                                                    // VPN được phép cho mod này, chỉ log để debug
                                                                    Log.d(TAG, "VPN detected during download but allowed for current mod");
                                                                }
                                                            }
                                                        } catch (Exception e) {
                                                            Log.e(TAG, "Error updating progress UI: " + e.getMessage());
                                                        }
                                                    }
                                                });

                                            try {
                                                Thread.sleep(500); // Tăng từ 100ms lên 500ms để giảm tải
                                            } catch (InterruptedException e) {
                                                break;
                                            }
                                        }
                                    } catch (Exception e) {
                                        Log.e(TAG, "Error in progress update thread: " + e.getMessage());
                                    }
                                }
                            });

                        // Track và bắt đầu thread cập nhật UI
                        activeThreads.add(progressUpdateThread);
                        progressUpdateThread.start();

                        // Thực hiện tải xuống thật sự
                        // Mô phỏng tiến trình kết nối (0-20%)
                        simulateProgress(0, 20, 500);

                        // Ghi đè trực tiếp lên file cũ
                        final String result = LibOnline(context, libPath);

                        // Mô phỏng tiến trình giải mã và cài đặt (80-95%)
                        simulateProgress(80, 95, 300);

                        // Dừng thread cập nhật UI
                        progressUpdateThread.interrupt();

                        // Hoàn thành tiến trình (95-100%)
                        simulateProgress(95, 100, 200);

                        mainHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        if ("ok".equals(result)) {
                                            try {
                                                // Đảm bảo hiển thị 100%
                                                downloadProgress = 100;
                                                if (progressText != null) {
                                                    progressText.setText("100%");
                                                }
                                                if (downloadBar != null) {
                                                    downloadBar.setProgress(100);
                                                }
                                                if (statusText != null) {
                                                    statusText.setText("Cài đặt thành công!");
                                                }

                                                // Hiển thị thông tin về thời gian tải
                                                long endTime = System.currentTimeMillis();
                                                long totalTime = endTime - startTime;
                                                Log.d(TAG, "Download completed in " + (totalTime / 1000.0) + " seconds");

                                                // Kiểm tra file đã được ghi đè thành công
                                                File libFile = new File(libPath);
                                                if (libFile.exists()) {
                                                    long fileSize = libFile.length();
                                                    String fileSizeStr = formatFileSize(fileSize);
                                                    Log.d(TAG, "Downloaded file size: " + fileSizeStr);
                                                }

                                                // Dừng animation và hiển thị icon hoàn thành
                                                if (rotateAnimation != null && loadingGearIcon != null) {
                                                    rotateAnimation.cancel();
                                                    loadingGearIcon.clearAnimation();

                                                    // Thay thế icon bánh răng bằng icon hoàn thành
                                                    loadingGearIcon.setText("✓");
                                                    loadingGearIcon.setTextColor(Color.parseColor(getColorGreen()));
                                                }
                                                if (libFile.exists()) {
                                                    try {
                                                        // Thử set permission, nhưng không crash nếu thất bại
                                                        libFile.setReadable(true, false);
                                                        libFile.setExecutable(true, false);
                                                    } catch (Exception permissionError) {
                                                        Log.w(TAG, "Could not set file permissions: " + permissionError.getMessage());
                                                        // Tiếp tục thực hiện, có thể vẫn load được
                                                    }

                                                    // Thêm crash protection cho System.load
                                                    try {
                                                        // Kiểm tra file integrity trước khi load
                                                        if (libFile.length() < 1024) {
                                                            throw new RuntimeException("Library file too small, possibly corrupted");
                                                        }

                                                        // Đợi một chút để đảm bảo game đã khởi tạo xong
                                                        Thread.sleep(2000);

                                                        // Load library với error handling
                                                        System.load(libPath);

                                                        Log.d(TAG, "Successfully loaded library: " + libPath);

                                                        // Đợi thêm để library khởi tạo
                                                        Thread.sleep(1000);

                                                        // OPTIMIZED: Tắt hoàn toàn VPN check sau khi tải mod xong để tránh lag
                                                        stopVpnCheck();
                                                        Log.d(TAG, "VPN check permanently disabled after mod loaded successfully");

                                                        // Dừng tất cả background processes sau khi load thành công
                                                        stopBackgroundProcessesAfterLogin();

                                                    } catch (UnsatisfiedLinkError e) {
                                                        Log.e(TAG, "UnsatisfiedLinkError loading library: " + e.getMessage());
                                                        throw new RuntimeException("Không thể load MOD: " + e.getMessage());
                                                    } catch (SecurityException e) {
                                                        Log.e(TAG, "SecurityException loading library: " + e.getMessage());
                                                        throw new RuntimeException("Lỗi bảo mật khi load MOD: " + e.getMessage());
                                                    } catch (Exception e) {
                                                        Log.e(TAG, "General exception loading library: " + e.getMessage());
                                                        throw new RuntimeException("Lỗi không xác định khi load MOD: " + e.getMessage());
                                                    }

                                                    if (dialog != null && dialog.isShowing()) {
                                                        dialog.dismiss();
                                                    }

                                                    hideGlobalBlockingOverlay();
                                                    stopAllAnimations();

                                                    // Hiển thị menu sau khi tải thành công
                                                    Handler menuHandler = new Handler();
                                                    activeHandlers.add(menuHandler);
                                                    menuHandler.postDelayed(new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                try {
                                                                    Menu menu = new Menu(context);
                                                                    menu.SetWindowManagerActivity();
                                                                    menu.ShowMenu();
                                                                } catch (Exception e) {
                                                                    Log.e(TAG, "Error showing menu: " + e.getMessage());
                                                                    showError(context, "Lỗi hiển thị menu: " + e.getMessage());
                                                                }
                                                            }
                                                        }, 1000);
                                                } else {
                                                    showError(context, "Thư viện không tồn tại sau khi tải");
                                                }
                                            } catch (Exception e) {
                                                hideGlobalBlockingOverlay();
                                                stopAllAnimations();
                                                Log.e(TAG, "Error loading library: " + e.getMessage());
                                                showError(context, "Lỗi tải thư viện: " + e.getMessage());
                                            }
                                        } else {
                                            hideGlobalBlockingOverlay();
                                            stopAllAnimations();

                                            // Dừng animation bánh răng và thay bằng icon lỗi
                                            if (rotateAnimation != null && loadingGearIcon != null) {
                                                rotateAnimation.cancel();
                                                loadingGearIcon.clearAnimation();
                                                loadingGearIcon.setText("⚠️");
                                                loadingGearIcon.setTextColor(Color.parseColor(getColorRed()));
                                            }

                                            showError(context, result != null ? result : "Lỗi không xác định");

                                            String lowerResult = result != null ? result.toLowerCase() : "";
                                            if (lowerResult.contains("phiên bản") ||
                                                lowerResult.contains("version") ||
                                                lowerResult.contains("cập nhật")) {
                                                new Handler().postDelayed(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            Activity activity = getActivity();
                                                            if (activity != null && !activity.isFinishing()) {
                                                                activity.finish();
                                                                android.os.Process.killProcess(android.os.Process.myPid());
                                                                System.exit(1);
                                                            }
                                                        }
                                                    }, 3000);
                                            } else {
                                                Handler recoveryHandler = new Handler();
                                                activeHandlers.add(recoveryHandler);
                                                recoveryHandler.postDelayed(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            if (dialog != null && dialog.isShowing()) {
                                                                dialog.dismiss();
                                                            }
                                                            showModSelector();
                                                        }
                                                    }, 2000);
                                            }
                                        }
                                    } catch (Exception e) {
                                        hideGlobalBlockingOverlay();
                                        stopAllAnimations();
                                        Log.e(TAG, "Error handling result: " + e.getMessage());
                                        showError(context, "Lỗi xử lý kết quả: " + e.getMessage());
                                    }
                                }
                            });
                    } catch (final Exception e) {
                        mainHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    hideGlobalBlockingOverlay();
                                    stopAllAnimations();

                                    // Dừng animation bánh răng và thay bằng icon lỗi
                                    if (rotateAnimation != null && loadingGearIcon != null) {
                                        rotateAnimation.cancel();
                                        loadingGearIcon.clearAnimation();
                                        loadingGearIcon.setText("⚠️");
                                        loadingGearIcon.setTextColor(Color.parseColor(getColorRed()));
                                    }

                                    showError(context, "Lỗi không xác định: " + e.getMessage());

                                    Handler errorHandler = new Handler();
                                    activeHandlers.add(errorHandler);
                                    errorHandler.postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (dialog != null && dialog.isShowing()) {
                                                    dialog.dismiss();
                                                }
                                                showModSelector();
                                            }
                                        }, 2000);
                                }
                            });
                    }
                }
            });

        // Track thread để cleanup
        activeThreads.add(downloadThread);
        downloadThread.start();
    }

    // Thêm nhãn hiển thị APP_IDENTIFIER ở góc dưới bên phải
    private static void addAppIdentifierLabel(FrameLayout rootLayout, float density) {
        Context context = getContext();
        if (context == null) return;

        try {
            // Lấy APP_IDENTIFIER từ native code
            String appIdentifier = GetAppIdentifier();
            if (appIdentifier == null || appIdentifier.isEmpty()) {
                appIdentifier = "Unknown";
            }

            // Tạo TextView hiển thị APP_IDENTIFIER
            TextView appIdLabel = new TextView(context);
            appIdLabel.setText("Version: " + appIdentifier);
            appIdLabel.setTextSize(10); // Kích thước nhỏ
            appIdLabel.setTextColor(isDarkMode ? Color.parseColor("#888888") : Color.parseColor("#AAAAAA")); // Màu xám nhạt
            appIdLabel.setGravity(Gravity.CENTER);
            appIdLabel.setPadding((int)(8 * density), (int)(4 * density), (int)(8 * density), (int)(4 * density));

            // Tạo background với góc bo tròn
            GradientDrawable background = new GradientDrawable();
            background.setColor(isDarkMode ? Color.parseColor("#33000000") : Color.parseColor("#22FFFFFF"));
            background.setCornerRadius(8 * density);
            background.setStroke(1, isDarkMode ? Color.parseColor("#44FFFFFF") : Color.parseColor("#22000000"));
            appIdLabel.setBackground(background);

            // Thiết lập vị trí ở góc dưới bên phải
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            );
            params.gravity = Gravity.BOTTOM | Gravity.END;
            params.setMargins(0, 0, (int)(16 * density), (int)(16 * density));
            appIdLabel.setLayoutParams(params);

            // Thêm vào rootLayout
            rootLayout.addView(appIdLabel);
        } catch (Exception e) {
            Log.e(TAG, "Error adding APP_IDENTIFIER label: " + e.getMessage());
        }
    }

    private static void showError(Context context, String message) {
        if (context == null) return;

        try {
            // Fallback to standard toast if context not available
            Toast.makeText(context, message, Toast.LENGTH_LONG).show();

            // iOS style toast (nếu có thể)
            if (context instanceof Activity) {
                final Activity activity = (Activity) context;
                try {
                    final LinearLayout toastLayout = new LinearLayout(context);
                    toastLayout.setOrientation(LinearLayout.VERTICAL);
                    toastLayout.setGravity(Gravity.CENTER);

                    GradientDrawable toastBg = new GradientDrawable();
                    toastBg.setColor(Color.parseColor("#222222")); // iOS toast color (darker)
                    toastBg.setCornerRadius(14); // iOS rounded corners
                    toastBg.setAlpha(230); // iOS semi-transparent
                    toastLayout.setBackground(toastBg);

                    int padding = (int)(14 * context.getResources().getDisplayMetrics().density);
                    toastLayout.setPadding(padding, padding, padding, padding);

                    TextView toastText = new TextView(context);
                    toastText.setText(message);
                    toastText.setTextSize(13); // iOS smaller text
                    toastText.setTextColor(Color.WHITE);
                    toastText.setGravity(Gravity.CENTER);
                    toastLayout.addView(toastText);

                    final FrameLayout container = new FrameLayout(context);
                    FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.WRAP_CONTENT,
                        FrameLayout.LayoutParams.WRAP_CONTENT
                    );
                    params.gravity = Gravity.CENTER | Gravity.BOTTOM;
                    params.bottomMargin = (int)(64 * context.getResources().getDisplayMetrics().density);
                    toastLayout.setLayoutParams(params);

                    container.addView(toastLayout);

                    // iOS style animation (sử dụng Handler thay vì withEndAction)
                    activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    final ViewGroup rootView = activity.getWindow().getDecorView().findViewById(android.R.id.content);
                                    rootView.addView(container);

                                    // iOS fades in and out
                                    toastLayout.setAlpha(0f);
                                    toastLayout.animate().alpha(1f).setDuration(200).start();

                                    new Handler().postDelayed(new Runnable() {
                                            @Override
                                            public void run() {
                                                try {
                                                    toastLayout.animate().alpha(0f).setDuration(200).start();
                                                    // Dùng handler thay vì withEndAction để tránh crash
                                                    new Handler().postDelayed(new Runnable() {
                                                            @Override
                                                            public void run() {
                                                                try {
                                                                    rootView.removeView(container);
                                                                } catch (Exception e) {
                                                                    Log.e(TAG, "Error removing toast: " + e.getMessage());
                                                                }
                                                            }
                                                        }, 200);
                                                } catch (Exception e) {
                                                    Log.e(TAG, "Error fading out toast: " + e.getMessage());
                                                    // Đảm bảo view được xóa ngay cả khi animation thất bại
                                                    try {
                                                        rootView.removeView(container);
                                                    } catch (Exception e2) {
                                                        Log.e(TAG, "Error removing toast after fade out failure: " + e2.getMessage());
                                                    }
                                                }
                                            }
                                        }, 2500);
                                } catch (Exception e) {
                                    Log.e(TAG, "Error showing custom toast: " + e.getMessage());
                                    // Không cần hiển thị toast thông thường vì đã gọi ở trên
                                }
                            }
                        });
                } catch (Exception e) {
                    Log.e(TAG, "Error in custom toast creation: " + e.getMessage());
                    // Đã hiển thị toast thông thường ở đầu hàm
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing any error message: " + e.getMessage());
            // Không thể làm gì thêm ở đây
        }
    }

    private static int adjustAlpha(int color, float factor) {
        try {
            int alpha = Math.round(Color.alpha(color) * factor);
            int red = Color.red(color);
            int green = Color.green(color);
            int blue = Color.blue(color);
            return Color.argb(alpha, red, green, blue);
        } catch (Exception e) {
            Log.e(TAG, "Error adjusting alpha: " + e.getMessage());
            return color; // Trả về màu gốc nếu có lỗi
        }
    }

    /**
     * Kiểm tra xem người dùng có đang sử dụng VPN hay không
     * @return true nếu VPN đang được bật, false nếu không
     */
    private static boolean isVpnEnabled() {
        try {
            List<String> networkList = new ArrayList<>();
            try {
                Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                if (interfaces == null) {
                    return false;
                }

                while (interfaces.hasMoreElements()) {
                    NetworkInterface networkInterface = interfaces.nextElement();
                    if (networkInterface != null && networkInterface.isUp()) {
                        String name = networkInterface.getName();
                        if (name != null) {
                            networkList.add(name);
                        }
                    }
                }
            } catch (SocketException ex) {
                Log.e(TAG, "SocketException checking network interfaces: " + ex.getMessage());
                return false;
            } catch (Exception ex) {
                Log.e(TAG, "Error checking network interfaces: " + ex.getMessage());
                return false;
            }

            return networkList.contains("tun0") || networkList.contains("ppp0") ||
                networkList.contains("tun1") || networkList.contains("ppp1");

        } catch (Exception e) {
            Log.e(TAG, "Error in isVpnEnabled: " + e.getMessage());
            return false;
        }
    }



    /**
     * Mô phỏng tiến trình tải từ startProgress đến endProgress
     * @param startProgress Tiến trình bắt đầu (%)
     * @param endProgress Tiến trình kết thúc (%)
     * @param stepDelay Thời gian trễ giữa các bước (ms)
     */
    private static void simulateProgress(int startProgress, int endProgress, int stepDelay) {
        try {
            // Đảm bảo giá trị hợp lệ
            startProgress = Math.max(0, Math.min(startProgress, 100));
            endProgress = Math.max(0, Math.min(endProgress, 100));

            // Nếu startProgress >= endProgress, không cần mô phỏng
            if (startProgress >= endProgress) {
                downloadProgress = endProgress;
                return;
            }

            // Đặt tiến trình bắt đầu
            downloadProgress = startProgress;

            // Tính toán số bước và kích thước bước
            int steps = Math.min(20, endProgress - startProgress); // Tối đa 20 bước
            int stepSize = (endProgress - startProgress) / steps;

            // Mô phỏng tiến trình
            for (int i = 1; i <= steps; i++) {
                try {
                    Thread.sleep(stepDelay / steps);
                } catch (InterruptedException e) {
                    Log.e(TAG, "Sleep interrupted: " + e.getMessage());
                    break;
                }

                // Cập nhật tiến trình
                downloadProgress = startProgress + (i * stepSize);
            }

            // Đảm bảo tiến trình cuối cùng đúng với endProgress
            downloadProgress = endProgress;
        } catch (Exception e) {
            Log.e(TAG, "Error simulating progress: " + e.getMessage());
            // Đảm bảo tiến trình được cập nhật ngay cả khi có lỗi
            downloadProgress = endProgress;
        }
    }

    /**
     * Định dạng kích thước file thành chuỗi dễ đọc
     * @param size Kích thước file tính bằng byte
     * @return Chuỗi đã định dạng (ví dụ: "1.23 MB")
     */
    private static String formatFileSize(long size) {
        try {
            if (size <= 0) return "0 B";

            final String[] units = new String[] { "B", "KB", "MB", "GB", "TB" };
            int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

            // Giới hạn digitGroups trong phạm vi của mảng units
            digitGroups = Math.min(digitGroups, units.length - 1);

            // Định dạng số với 2 chữ số thập phân
            double value = size / Math.pow(1024, digitGroups);
            return String.format("%.2f %s", value, units[digitGroups]);
        } catch (Exception e) {
            Log.e(TAG, "Error formatting file size: " + e.getMessage());
            return size + " B"; // Trả về giá trị đơn giản nếu có lỗi
        }
    }

    // 🛡️ GAME GUARDIAN DETECTION IMPLEMENTATION

    /**
     * Thực hiện Game Guardian detection
     */
    private static void performGameGuardianCheck() {
        Thread detectionThread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // Delay để app khởi động hoàn toàn
                    Thread.sleep(1500);

                    Log.d(TAG, "🛡️ Starting Game Guardian detection...");

                    final GameGuardianDetector.DetectionResult result =
                        GameGuardianDetector.detectGameGuardian();

                    if (result.detected && result.confidence >= 70) {
                        Log.w(TAG, "🚨 GAME GUARDIAN DETECTED!");
                        Log.w(TAG, "Method: " + result.method);
                        Log.w(TAG, "Confidence: " + result.confidence + "%");
                        Log.w(TAG, "Details: " + result.details);

                        mainHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                handleGameGuardianDetected(result);
                            }
                        });
                    } else {
                        Log.d(TAG, "✅ Device is clean - No Game Guardian detected");
                        Log.d(TAG, "Detection method: " + result.method);
                        Log.d(TAG, "Confidence: " + result.confidence + "%");
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Game Guardian detection thread error: " + e.getMessage());
                }
            }
        });

        detectionThread.setDaemon(true);
        detectionThread.start();
        activeThreads.add(detectionThread);
    }

    /**
     * Xử lý khi phát hiện Game Guardian
     */
    private static void handleGameGuardianDetected(GameGuardianDetector.DetectionResult result) {
        Context context = getContext();
        if (context == null) return;

        try {
            Log.e(TAG, "🚨 HANDLING GAME GUARDIAN DETECTION");

            // Tạo message chi tiết
            String message = "🚨 Phát hiện Game Guardian!\n\n" +
                           "Ứng dụng không thể chạy khi Game Guardian đang hoạt động.\n" +
                           "Vui lòng tắt Game Guardian và khởi động lại ứng dụng.\n\n" +
                           "📊 Thông tin phát hiện:\n" +
                           "• Phương pháp: " + result.method + "\n" +
                           "• Độ tin cậy: " + result.confidence + "%\n" +
                           "• Chi tiết: " + result.details + "\n\n" +
                           "⚠️ Ứng dụng sẽ đóng sau 5 giây.";

            // Show error dialog
            showError(context, message);

            // Graceful shutdown sau 5 giây
            new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        Log.w(TAG, "🚨 Closing app due to Game Guardian detection");

                        Activity activity = getActivity();
                        if (activity != null && !activity.isFinishing()) {
                            activity.finish();
                        }

                        // Force close sau 1 giây nữa
                        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                android.os.Process.killProcess(android.os.Process.myPid());
                                System.exit(1);
                            }
                        }, 1000);

                    } catch (Exception e) {
                        Log.e(TAG, "Error during app shutdown: " + e.getMessage());
                        // Force exit nếu có lỗi
                        android.os.Process.killProcess(android.os.Process.myPid());
                    }
                }
            }, 5000);

        } catch (Exception e) {
            Log.e(TAG, "Error handling Game Guardian detection: " + e.getMessage());
            // Fallback - force close ngay lập tức
            Activity activity = getActivity();
            if (activity != null) {
                activity.finish();
            }
        }
    }
}





