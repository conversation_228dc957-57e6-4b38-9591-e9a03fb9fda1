package com.hmod.vip;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

public class MainActivity extends Activity {

    //Only if you have changed MainActivity to yours and you wanna call game's activity.

    public boolean hasLaunched = false;

    //To call onCreate, please refer to README.md
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 🧪 TEST GAME GUARDIAN DETECTION (Remove in production)
        // GameGuardianTest.testDetection(this);
        // GameGuardianTest.testNativeMethods();

        Main.Start(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Main.onAppResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        Main.onAppPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Main.onAppDestroy();
    }
}
