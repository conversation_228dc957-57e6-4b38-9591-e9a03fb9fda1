package com.hmod.vip;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;

import java.util.LinkedHashSet;
import java.util.Set;

public class Preferences {
    private static SharedPreferences sharedPreferences;
    private static Preferences prefsInstance;
    public static Context context;
    public static boolean loadPref = true, isExpanded;

    private static final String LENGTH = "_length";
    private static final String DEFAULT_STRING_VALUE = "";
    private static final int DEFAULT_INT_VALUE = 0;
    private static final double DEFAULT_DOUBLE_VALUE = 0d;
    private static final float DEFAULT_FLOAT_VALUE = 0f;
    private static final long DEFAULT_LONG_VALUE = 0L;
    private static final boolean DEFAULT_BOOLEAN_VALUE = false;

    // Native method for notifying changes
    public static native void Changes(Context con, int fNum, String fName, int i, boolean bool, String str);

    // Improved feature-specific methods
    public static void changeFeatureBool(String featureName, int featureNum, boolean bool) {
        String uniqueKey = "feature_bool_" + featureNum + "_" + featureName;
        Preferences.with(context).writeBoolean(uniqueKey, bool);

        // Special handling for specific feature numbers
        if (featureNum == -1) {
            loadPref = bool;
        }
        if (featureNum == -3) {
            isExpanded = bool;
        }

        Changes(context, featureNum, featureName, 0, bool, null);
    }

    public static boolean loadPrefBool(String featureName, int featureNum, boolean bDef) {
        String uniqueKey = "feature_bool_" + featureNum + "_" + featureName;
        boolean bool = Preferences.with(context).readBoolean(uniqueKey, bDef);

        // Special handling for specific feature numbers
        if (featureNum == -1) {
            loadPref = bool;
        }
        if (featureNum == -3) {
            isExpanded = bool;
        }

        Changes(context, featureNum, featureName, 0, bool, null);
        return bool;
    }

    public static void changeFeatureInt(String featureName, int featureNum, int value) {
        String uniqueKey = "feature_int_" + featureNum + "_" + featureName;
        Preferences.with(context).writeInt(uniqueKey, value);
        Changes(context, featureNum, featureName, value, false, null);
    }

    public static int loadPrefInt(String featureName, int featureNum) {
        String uniqueKey = "feature_int_" + featureNum + "_" + featureName;
        int value = Preferences.with(context).readInt(uniqueKey);
        Changes(context, featureNum, featureName, value, false, null);
        return value;
    }

    public static void changeFeatureString(String featureName, int featureNum, String str) {
        String uniqueKey = "feature_string_" + featureNum + "_" + featureName;
        Preferences.with(context).writeString(uniqueKey, str);
        Changes(context, featureNum, featureName, 0, false, str);
    }

    public static String loadPrefString(String featureName, int featureNum) {
        String uniqueKey = "feature_string_" + featureNum + "_" + featureName;
        String value = Preferences.with(context).readString(uniqueKey);
        Changes(context, featureNum, featureName, 0, false, value);
        return value;
    }

    // Constructors
    private Preferences(Context context) {
        sharedPreferences = context.getApplicationContext().getSharedPreferences(
            context.getPackageName() + "_preferences",
            Context.MODE_PRIVATE
        );
    }

    private Preferences(Context context, String preferencesName) {
        sharedPreferences = context.getApplicationContext().getSharedPreferences(
            preferencesName,
            Context.MODE_PRIVATE
        );
    }

    // Singleton methods
    public static Preferences with(Context context) {
        if (prefsInstance == null) {
            prefsInstance = new Preferences(context);
        }
        return prefsInstance;
    }

    public static Preferences with(Context context, boolean forceInstantiation) {
        if (forceInstantiation) {
            prefsInstance = new Preferences(context);
        }
        return prefsInstance;
    }

    public static Preferences with(Context context, String preferencesName) {
        if (prefsInstance == null) {
            prefsInstance = new Preferences(context, preferencesName);
        }
        return prefsInstance;
    }

    public static Preferences with(Context context, String preferencesName, boolean forceInstantiation) {
        if (forceInstantiation) {
            prefsInstance = new Preferences(context, preferencesName);
        }
        return prefsInstance;
    }

    // Existing methods for reading and writing various types

    // String methods
    public String readString(String what) {
        return sharedPreferences.getString(what, DEFAULT_STRING_VALUE);
    }

    public String readString(int what) {
        try {
            return sharedPreferences.getString(String.valueOf(what), DEFAULT_STRING_VALUE);
        } catch (ClassCastException ex) {
            return "";
        }
    }

    public String readString(String what, String defaultString) {
        return sharedPreferences.getString(what, defaultString);
    }

    public void writeString(String where, String what) {
        sharedPreferences.edit().putString(where, what).apply();
    }

    public void writeString(int where, String what) {
        sharedPreferences.edit().putString(String.valueOf(where), what).apply();
    }

    // Int methods
    public int readInt(String what) {
        return sharedPreferences.getInt(what, DEFAULT_INT_VALUE);
    }

    public int readInt(int what) {
        try {
            return sharedPreferences.getInt(String.valueOf(what), DEFAULT_INT_VALUE);
        } catch (ClassCastException ex) {
            return 0;
        }
    }

    public int readInt(String what, int defaultInt) {
        return sharedPreferences.getInt(what, defaultInt);
    }

    public void writeInt(String where, int what) {
        sharedPreferences.edit().putInt(where, what).apply();
    }

    public void writeInt(int where, int what) {
        sharedPreferences.edit().putInt(String.valueOf(where), what).apply();
    }

    // Boolean methods
    public boolean readBoolean(String what) {
        return sharedPreferences.getBoolean(what, DEFAULT_BOOLEAN_VALUE);
    }

    public boolean readBoolean(int what) {
        return sharedPreferences.getBoolean(String.valueOf(what), DEFAULT_BOOLEAN_VALUE);
    }

    public boolean readBoolean(String what, boolean defaultBoolean) {
        return sharedPreferences.getBoolean(what, defaultBoolean);
    }

    public boolean readBoolean(int what, boolean defaultBoolean) {
        try {
            return sharedPreferences.getBoolean(String.valueOf(what), defaultBoolean);
        } catch (ClassCastException ex) {
            return defaultBoolean;
        }
    }

    public void writeBoolean(String where, boolean what) {
        sharedPreferences.edit().putBoolean(where, what).apply();
    }

    public void writeBoolean(int where, boolean what) {
        sharedPreferences.edit().putBoolean(String.valueOf(where), what).apply();
    }

    // Other existing methods (double, float, long, string set) remain the same as in the original implementation

    // Utility methods
    public void remove(final String key) {
        if (contains(key + LENGTH)) {
            int stringSetLength = readInt(key + LENGTH);
            if (stringSetLength >= 0) {
                sharedPreferences.edit().remove(key + LENGTH).apply();
                for (int i = 0; i < stringSetLength; i++) {
                    sharedPreferences.edit().remove(key + "[" + i + "]").apply();
                }
            }
        }
        sharedPreferences.edit().remove(key).apply();
    }

    public boolean contains(final String key) {
        return sharedPreferences.contains(key);
    }

    public void clear() {
        sharedPreferences.edit().clear().apply();
    }
}
