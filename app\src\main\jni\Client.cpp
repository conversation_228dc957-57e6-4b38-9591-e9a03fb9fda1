#include <list>
#include <vector>
#include <string.h>
#include <pthread.h>
#include <thread>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <dlfcn.h>
#include "Includes/Logger.h"
#include "Includes/obfuscate.h"
#include "Includes/Utils.h"
#include "KittyMemory/MemoryPatch.h"
#include "Menu/Setup.h"
#include "AES/AES.cpp"

#define targetLibName OBFUSCATE("libil2cpp.so")
#define GAME_CODE "aovvn" // Game code để xác định game
#define APP_IDENTIFIER "aovvn_ESP2" // Định danh ứng dụng để phân biệt giữa các APK

#include "Includes/Macros.h"
#include "Tools/Tools.h"
#include "TranHuong/IL2Cpp.h"
#include "ARMPatch.cpp"
#include "StrEnc.h"
#include <curl/curl.h>
#include "json.hpp"
#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/err.h>
#include <openssl/md5.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/ptrace.h>
#include <dirent.h>

using json = nlohmann::ordered_json;
using namespace std;

bool bValid = false;
std::string g_Auth, g_Token;
bool check = false;

#include <fstream>
#include "base64/base64.cpp"

// 🛡️ GAME GUARDIAN DETECTOR - Import vào Client.cpp để load ngay khi app start
#include "GameGuardianDetector.h"

struct My_Patches {
    MemoryPatch HM;
} hexPatches;



uintptr_t il2cppMap;

struct MemoryStruct {
    char *memory;
    size_t size;
};

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t realsize = size * nmemb;
    struct MemoryStruct *mem = (struct MemoryStruct *)userp;
    
    char *ptr = (char *)realloc(mem->memory, mem->size + realsize + 1);
    if(!ptr) {
        return 0;
    }
    
    mem->memory = ptr;
    memcpy(&(mem->memory[mem->size]), contents, realsize);
    mem->size += realsize;
    mem->memory[mem->size] = 0;
    
    return realsize;
}

const char *g_key = OBFUSCATE("094412704612345689");
const char *g_iv = OBFUSCATE("0123456789012345");

string EncryptionAES(const string& strSrc) {
    size_t length = strSrc.length();
    int block_num = length / BLOCK_SIZE + 1;
    char* szDataIn = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataIn, 0x00, block_num * BLOCK_SIZE + 1);
    strcpy(szDataIn, strSrc.c_str());

    int k = length % BLOCK_SIZE;
    int j = length / BLOCK_SIZE;
    int padding = BLOCK_SIZE - k;
    for (int i = 0; i < padding; i++) {
        szDataIn[j * BLOCK_SIZE + k + i] = padding;
    }
    szDataIn[block_num * BLOCK_SIZE] = '\0';

    char *szDataOut = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataOut, 0, block_num * BLOCK_SIZE + 1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Encrypt(szDataIn, szDataOut, block_num * BLOCK_SIZE, AES::CBC);
    string str = base64_encode((unsigned char*) szDataOut,
                               block_num * BLOCK_SIZE);
    delete[] szDataIn;
    delete[] szDataOut;
    return str;
}

string DecryptionAES(const string& strSrc) {
    string strData = base64_decode(strSrc);
    size_t length = strData.length();
    char *szDataIn = new char[length + 1];
    memcpy(szDataIn, strData.c_str(), length+1);
    char *szDataOut = new char[length + 1];
    memcpy(szDataOut, strData.c_str(), length+1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Decrypt(szDataIn, szDataOut, length, AES::CBC);

    if (0x00 < szDataOut[length - 1] <= 0x16) {
        int tmp = szDataOut[length - 1];
        for (int i = length - 1; i >= length - tmp; i--) {
            if (szDataOut[i] != tmp) {
                memset(szDataOut, 0, length);
                break;
            }
            else
                szDataOut[i] = 0;
        }
    }
    string strDest(szDataOut);
    delete[] szDataIn;
    delete[] szDataOut;
    return strDest;
}



static std::string GetPackageName() {
    char application_id[256] = {0};
    FILE *fp = fopen("/proc/self/cmdline", "r");
    if (fp) {
        fread(application_id, sizeof(application_id) - 1, 1, fp);
        fclose(fp);
    }
    return std::string(application_id);
}

// Hàm lấy tên file từ đường dẫn
static std::string getFileName(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    if (pos != std::string::npos) {
        return path.substr(pos + 1);
    }
    return path;
}

// Hàm lấy thư mục cha
static std::string getDirName(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    if (pos != std::string::npos) {
        return path.substr(0, pos);
    }
    return "";
}

// Kiểm tra thư mục có tồn tại không
static bool dirExists(const std::string& path) {
    struct stat info;
    if (stat(path.c_str(), &info) != 0) {
        return false;
    }
    return (info.st_mode & S_IFDIR) != 0;
}

// Tạo thư mục
static bool createDir(const std::string& path) {
    return mkdir(path.c_str(), 0755) == 0;
}

void *hack_thread(void *) {
/*

    std::string cacheDir = "/data/data/" + GetPackageName() + "/cache";
    std::string cacheCleanCmd = "find " + cacheDir + " -type f -delete";
    system(cacheCleanCmd.c_str());
*/
    while (!il2cppMap) {
        il2cppMap = Tools::GetBaseAddress("libil2cpp.so");
        sleep(1);
    }
    IL2Cpp::Il2CppAttach();


    MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"000080D2C0035FD6").Modify();
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"000080D2C0035FD6").Modify();

    return NULL;
}

__attribute__((constructor))
void lib_main() {
    pthread_t ptid;
    pthread_create(&ptid, NULL, hack_thread, NULL);
}

std::string jstringToString(JNIEnv* env, jstring jstr) {
    const char* strChars = env->GetStringUTFChars(jstr, NULL);
    std::string str(strChars);
    env->ReleaseStringUTFChars(jstr, strChars);
    return str;
}

extern "C" {

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Main_LibOnline(JNIEnv *env, jclass clazz, jobject mContext, jstring path) {
    try {
        std::string pa = jstringToString(env, path);
        std::string libname = getFileName(pa);
        bool libOnline = false;
        std::string errMsg = "Không thể kết nối đến máy chủ";

        struct MemoryStruct chunk{};
        chunk.memory = (char *) malloc(1);
        if (!chunk.memory) {
            return env->NewStringUTF("Lỗi: Không thể cấp phát bộ nhớ");
        }
        chunk.size = 0;

        CURL *curl = curl_easy_init();
        if (curl) {
            try {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
                curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/login-app.php")).c_str());
                curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
                curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
/*
                // Thêm timeout để tránh hang
                curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
                curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 10L);
                curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, 1024L);
                curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, 10L);
*/
                struct curl_slist *headers = NULL;
                headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
                curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

                char data[4096];
                snprintf(data, sizeof(data), "game=lqm&libname=%s&gamecode=%s&app_identifier=%s",
                         libname.c_str(), GAME_CODE, APP_IDENTIFIER);

                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
                curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
                curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
                curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
                curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

                CURLcode res = curl_easy_perform(curl);
                if (res == CURLE_OK && chunk.memory && chunk.size > 0) {
                    try {
                        // Đảm bảo chunk.memory kết thúc bằng null
                        char *temp = (char *)realloc(chunk.memory, chunk.size + 1);
                        if (temp) {
                            chunk.memory = temp;
                            chunk.memory[chunk.size] = 0;
                        }

                        json response = json::parse(chunk.memory);

                        if(response["status"] == "success") {
                            if (response.contains("data") && response.contains("version")) {
                                try {
                                    std::string dex = response["data"].get<std::string>();
                                    dex = base64_decode(dex);

                                    // Kiểm tra xem dữ liệu có hợp lệ không
                                    if (dex.empty()) {
                                        errMsg = "Dữ liệu MOD không hợp lệ hoặc trống";
                                    } else {
                                        // Lưu phiên bản
                                        std::string version_file = pa + ".version";
                                        ofstream vfile(version_file.c_str());
                                        if (vfile.is_open()) {
                                            vfile << response["version"].get<std::string>();
                                            vfile.close();
                                        }

                                        // Lưu file MOD
                                        ofstream outfile(pa.c_str(), ios::binary);
                                        if (outfile.is_open()) {
                                            outfile.write(dex.c_str(), dex.length());
                                            outfile.close();
                                            libOnline = true;
                                        } else {
                                            errMsg = "Không thể mở file để ghi: " + pa;
                                        }
                                    }
                                } catch (std::exception &e) {
                                    errMsg = "Lỗi khi xử lý dữ liệu MOD: ";
                                    errMsg += e.what();
                                }
                            } else {
                                errMsg = "Phản hồi từ server không đầy đủ";
                            }
                        } else {
                            if (response.contains("message")) {
                                errMsg = response["message"].get<std::string>();
                            } else {
                                errMsg = "Lỗi không xác định từ server";
                            }
                        }
                    } catch (json::exception &e) {
                        errMsg = "Lỗi phân tích JSON: ";
                        errMsg += e.what();
                    } catch (std::exception &e) {
                        errMsg = "Lỗi xử lý: ";
                        errMsg += e.what();
                    }
                } else {
                    if (res != CURLE_OK) {
                        errMsg = "Lỗi kết nối: ";
                        errMsg += curl_easy_strerror(res);
                    } else {
                        errMsg = "Không nhận được dữ liệu từ server";
                    }
                }

                curl_slist_free_all(headers);
                curl_easy_cleanup(curl);
            } catch (std::exception &e) {
                if (curl) curl_easy_cleanup(curl);
                errMsg = "Lỗi CURL: ";
                errMsg += e.what();
            }
        } else {
            errMsg = "Không thể khởi tạo CURL";
        }

        if(chunk.memory) {
            free(chunk.memory);
        }

        return libOnline ? env->NewStringUTF("ok") : env->NewStringUTF(errMsg.c_str());
    } catch (std::exception &e) {
        // Bắt tất cả các ngoại lệ có thể xảy ra
        std::string safeErrorMsg = "Lỗi không xác định: ";
        safeErrorMsg += e.what();
        return env->NewStringUTF(safeErrorMsg.c_str());
    }
}

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Main_GetAppIdentifier(JNIEnv *env, jclass clazz) {
    return env->NewStringUTF(APP_IDENTIFIER);
}

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Main_CheckModStatus(JNIEnv *env, jclass clazz) {
    try {
        struct MemoryStruct chunk{};
        chunk.memory = (char *) malloc(1);

        if (!chunk.memory) {
            return env->NewStringUTF("{\"status\":\"error\",\"message\":\"Không thể cấp phát bộ nhớ\"}");
        }

        chunk.size = 0;

        std::string errMsg = "{\"status\":\"error\",\"message\":\"Không thể kết nối đến máy chủ\"}";
        CURL *curl = curl_easy_init();

        if (curl) {
            try {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
                curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/login-app.php")).c_str());
                curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
                curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
                
                struct curl_slist *headers = NULL;
                headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
                curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

                std::string postData = "check_status=1&gamecode=" + std::string(GAME_CODE) + "&app_identifier=" + std::string(APP_IDENTIFIER);
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, postData.c_str());
                curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
                curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
                curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
                curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

                CURLcode res = curl_easy_perform(curl);
                if (res != CURLE_OK) {
                    errMsg = "{\"status\":\"error\",\"message\":\"Lỗi kết nối: ";
                    errMsg += curl_easy_strerror(res);
                    errMsg += "\"}";
                }

                curl_slist_free_all(headers);
                curl_easy_cleanup(curl);
            } catch (std::exception &e) {
                if (curl) curl_easy_cleanup(curl);
                errMsg = "{\"status\":\"error\",\"message\":\"Lỗi CURL: ";
                errMsg += e.what();
                errMsg += "\"}";
            }
        }

        if(chunk.memory && chunk.size > 0) {
            try {
                // Đảm bảo chunk.memory kết thúc bằng null
                char *temp = (char *)realloc(chunk.memory, chunk.size + 1);
                if (temp) {
                    chunk.memory = temp;
                    chunk.memory[chunk.size] = 0;
                }

                // Kiểm tra xem phản hồi có phải là JSON hợp lệ không
                json response = json::parse(chunk.memory);

                // Kiểm tra xem phản hồi có trường status không
                if (!response.contains("status")) {
                    // Nếu không có trường status, thêm vào
                    response["status"] = "success";
                }

                // Nếu không có trường data, thêm một đối tượng trống
                if (!response.contains("data")) {
                    response["data"] = json::object();
                }

                // Kiểm tra xem data có rỗng không
                if (response["data"].is_object() && response["data"].empty()) {
                    // Nếu data là đối tượng rỗng, thêm thông báo
                    if (!response.contains("message")) {
                        response["message"] = "Không có MOD nào khả dụng cho ứng dụng này.";
                    }
                }

                // Trả về phản hồi đã được kiểm tra
                std::string validResponse = response.dump();
                jstring result = env->NewStringUTF(validResponse.c_str());
                free(chunk.memory);
                return result;
            } catch (json::exception &e) {
                // Nếu phản hồi không phải là JSON hợp lệ, trả về JSON lỗi hợp lệ
                errMsg = "{\"status\":\"error\",\"message\":\"Phản hồi không hợp lệ: ";
                errMsg += e.what();
                errMsg += "\",\"data\":{}}";
                free(chunk.memory);
                return env->NewStringUTF(errMsg.c_str());
            } catch (std::exception &e) {
                // Bắt các ngoại lệ khác
                errMsg = "{\"status\":\"error\",\"message\":\"Lỗi xử lý: ";
                errMsg += e.what();
                errMsg += "\",\"data\":{}}";
                free(chunk.memory);
                return env->NewStringUTF(errMsg.c_str());
            }
        }

        if (chunk.memory) {
            free(chunk.memory);
        }

        // Đảm bảo trả về JSON hợp lệ ngay cả khi có lỗi
        if (errMsg.find("{") != 0) {
            errMsg = "{\"status\":\"error\",\"message\":\"" + errMsg + "\",\"data\":{}}";
        }

        return env->NewStringUTF(errMsg.c_str());
    } catch (std::exception &e) {
        // Bắt tất cả các ngoại lệ có thể xảy ra
        std::string safeErrorMsg = "{\"status\":\"error\",\"message\":\"Lỗi không xác định: ";
        safeErrorMsg += e.what();
        safeErrorMsg += "\",\"data\":{}}";
        return env->NewStringUTF(safeErrorMsg.c_str());
    }
}

// 🛡️ GAME GUARDIAN DETECTOR IMPLEMENTATION
// Moved to Client.cpp để load ngay khi app start

class GameGuardianDetector {
private:
    // Obfuscated strings để tránh detection
    static const char* getObfuscatedString(int index) {
        static const char* strings[] = {
            OBFUSCATE("TracerPid:"),      // 0
            OBFUSCATE("gdb"),             // 1
            OBFUSCATE("lldb"),            // 2
            OBFUSCATE("strace"),          // 3
            OBFUSCATE("android_studio"),  // 4
            OBFUSCATE("idea"),            // 5
            OBFUSCATE("libgg.so"),        // 6
            OBFUSCATE("GameGuardian"),    // 7
            OBFUSCATE("game_cih"),        // 8
            OBFUSCATE("/proc/"),          // 9
            OBFUSCATE("/cmdline"),        // 10
            OBFUSCATE("/maps"),           // 11
            OBFUSCATE("/fd"),             // 12
            OBFUSCATE("/mem"),            // 13
            OBFUSCATE("/status")          // 14
        };
        return strings[index];
    }

    // Kiểm tra xem process có phải debugger hợp lệ không
    static bool isValidDebugger(int pid) {
        try {
            std::string cmdlinePath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(10);
            std::ifstream cmdlineFile(cmdlinePath.c_str());

            if (!cmdlineFile.is_open()) {
                return false;
            }

            std::string cmdline;
            std::getline(cmdlineFile, cmdline);
            cmdlineFile.close();

            if (cmdline.empty()) {
                return false;
            }

            // Kiểm tra các debugger hợp lệ
            const char* legitimateDebuggers[] = {
                getObfuscatedString(1),  // gdb
                getObfuscatedString(2),  // lldb
                getObfuscatedString(3),  // strace
                getObfuscatedString(4),  // android_studio
                getObfuscatedString(5)   // idea
            };

            for (int i = 0; i < 5; i++) {
                if (cmdline.find(legitimateDebuggers[i]) != std::string::npos) {
                    return true;
                }
            }

            return false;
        } catch (...) {
            return false;
        }
    }

    // Kiểm tra đặc điểm của Game Guardian
    static bool checkGameGuardianCharacteristics(int pid) {
        try {
            // Kiểm tra memory maps
            std::string mapsPath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(11);
            std::ifstream mapsFile(mapsPath.c_str());

            if (mapsFile.is_open()) {
                std::string line;
                while (std::getline(mapsFile, line)) {
                    // Tìm signature của Game Guardian
                    if (line.find(getObfuscatedString(6)) != std::string::npos ||  // libgg.so
                        line.find(getObfuscatedString(7)) != std::string::npos ||  // GameGuardian
                        line.find(getObfuscatedString(8)) != std::string::npos) {  // game_cih
                        mapsFile.close();
                        return true;
                    }
                }
                mapsFile.close();
            }

            // Kiểm tra file descriptors
            std::string fdPath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(12);
            DIR* fdDir = opendir(fdPath.c_str());

            if (fdDir != nullptr) {
                int memoryAccessCount = 0;
                struct dirent* entry;

                while ((entry = readdir(fdDir)) != nullptr) {
                    if (entry->d_name[0] == '.') continue;

                    std::string fdLinkPath = fdPath + "/" + entry->d_name;
                    char linkTarget[256];
                    ssize_t linkLen = readlink(fdLinkPath.c_str(), linkTarget, sizeof(linkTarget) - 1);

                    if (linkLen > 0) {
                        linkTarget[linkLen] = '\0';
                        std::string target(linkTarget);

                        // Game Guardian thường mở nhiều /proc/*/mem files
                        if (target.find(getObfuscatedString(9)) != std::string::npos &&
                            target.find(getObfuscatedString(13)) != std::string::npos) {
                            memoryAccessCount++;
                        }
                    }
                }
                closedir(fdDir);

                // Game Guardian thường mở > 2 memory access FDs
                if (memoryAccessCount > 2) {
                    return true;
                }
            }

            return false;
        } catch (...) {
            return true; // Lỗi = nghi ngờ
        }
    }

public:
    // 🛡️ CORE DETECTION METHOD
    static int detectGameGuardianCore() {
        try {
            std::string statusPath = getObfuscatedString(9);
            statusPath += "self";
            statusPath += getObfuscatedString(14);

            std::ifstream statusFile(statusPath.c_str());
            if (!statusFile.is_open()) {
                return 50; // Không đọc được = nghi ngờ
            }

            std::string line;
            while (std::getline(statusFile, line)) {
                if (line.substr(0, 9) == getObfuscatedString(0)) { // TracerPid:
                    std::string pidStr = line.substr(10);

                    // Loại bỏ whitespace
                    pidStr.erase(0, pidStr.find_first_not_of(" \t"));
                    pidStr.erase(pidStr.find_last_not_of(" \t") + 1);

                    int tracerPid = std::atoi(pidStr.c_str());
                    statusFile.close();

                    if (tracerPid != 0) {
                        // Có process đang trace
                        if (!isValidDebugger(tracerPid)) {
                            if (checkGameGuardianCharacteristics(tracerPid)) {
                                return 95; // High confidence - Game Guardian detected
                            }
                            return 75; // Medium confidence - Suspicious tracer
                        }
                        return 10; // Low confidence - Valid debugger
                    }
                    return 0; // No tracer
                }
            }
            statusFile.close();
            return 0; // No TracerPid found
        } catch (...) {
            return 60; // Exception = suspicious
        }
    }

    // 🛡️ MEMORY PROTECTION TEST
    static bool detectMemoryTampering() {
        try {
            const uint32_t MAGIC_VALUE = 0x12345678;
            volatile uint32_t testValue = MAGIC_VALUE;

            // Monitor trong 5 giây
            for (int i = 0; i < 100; i++) {
                if (testValue != MAGIC_VALUE) {
                    return true; // Memory bị tamper
                }
                usleep(50000); // 50ms
            }

            return false; // Không có tampering
        } catch (...) {
            return false; // Lỗi = không detect được
        }
    }

    // 🛡️ ANTI-DEBUGGING SETUP
    static int setupAntiDebug() {
        try {
            // Self-trace để ngăn attachment
            long result = ptrace(PTRACE_TRACEME, 0, 0, 0);
            if (result == -1) {
                return 1; // Đã bị trace = có Game Guardian
            }
            return 0; // OK
        } catch (...) {
            return 1; // Lỗi = nghi ngờ
        }
    }

    // 🛡️ PROCESS ENUMERATION CHECK
    static bool detectSuspiciousProcesses() {
        try {
            DIR* procDir = opendir("/proc");
            if (procDir == nullptr) {
                return false;
            }

            struct dirent* entry;
            int suspiciousCount = 0;

            while ((entry = readdir(procDir)) != nullptr) {
                // Chỉ check numeric directories (PIDs)
                if (!isdigit(entry->d_name[0])) continue;

                std::string cmdlinePath = "/proc/";
                cmdlinePath += entry->d_name;
                cmdlinePath += "/cmdline";

                std::ifstream cmdlineFile(cmdlinePath.c_str());
                if (cmdlineFile.is_open()) {
                    std::string cmdline;
                    std::getline(cmdlineFile, cmdline);
                    cmdlineFile.close();

                    // Tìm process names nghi ngờ
                    if (cmdline.length() <= 3 ||  // Tên quá ngắn
                        cmdline.find("gg") != std::string::npos ||
                        cmdline.find("cih") != std::string::npos) {
                        suspiciousCount++;
                    }
                }
            }
            closedir(procDir);

            return suspiciousCount > 2; // > 2 process nghi ngờ
        } catch (...) {
            return false;
        }
    }
};

// 🛡️ JNI EXPORTS FOR GAME GUARDIAN DETECTION
JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeDetectGameGuardian(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectGameGuardianCore();
}

JNIEXPORT jboolean JNICALL
Java_com_hmod_vip_Main_nativeDetectMemoryTampering(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectMemoryTampering();
}

JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeSetupAntiDebug(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::setupAntiDebug();
}

JNIEXPORT jboolean JNICALL
Java_com_hmod_vip_Main_nativeDetectSuspiciousProcesses(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectSuspiciousProcesses();
}

JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    vm->GetEnv((void **) &env, JNI_VERSION_1_6);
    return JNI_VERSION_1_6;
}

} // extern "C"



