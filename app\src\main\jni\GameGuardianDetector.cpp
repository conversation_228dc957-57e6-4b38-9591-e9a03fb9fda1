#include <jni.h>
#include <unistd.h>
#include <sys/ptrace.h>
#include <fstream>
#include <string>
#include <vector>
#include <dirent.h>
#include <sys/stat.h>
#include <cstring>
#include <cstdlib>
#include <iostream>
#include <sstream>
#include "Includes/Logger.h"
#include "Includes/obfuscate.h"

// 🛡️ GAME GUARDIAN DETECTOR CLASS
class GameGuardianDetector {
private:
    // Obfuscated strings để tránh detection
    static const char* getObfuscatedString(int index) {
        static const char* strings[] = {
            OBFUSCATE("TracerPid:"),      // 0
            OBFUSCATE("gdb"),             // 1  
            OBFUSCATE("lldb"),            // 2
            OBFUSCATE("strace"),          // 3
            OBFUSCATE("android_studio"),  // 4
            OBFUSCATE("idea"),            // 5
            OBFUSCATE("libgg.so"),        // 6
            OBFUSCATE("GameGuardian"),    // 7
            OBFUSCATE("game_cih"),        // 8
            OBFUSCATE("/proc/"),          // 9
            OBFUSCATE("/cmdline"),        // 10
            OBFUSCATE("/maps"),           // 11
            OBFUSCATE("/fd"),             // 12
            OBFUSCATE("/mem"),            // 13
            OBFUSCATE("/status")          // 14
        };
        return strings[index];
    }
    
    // Kiểm tra xem process có phải debugger hợp lệ không
    static bool isValidDebugger(int pid) {
        try {
            std::string cmdlinePath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(10);
            std::ifstream cmdlineFile(cmdlinePath.c_str());
            
            if (!cmdlineFile.is_open()) {
                return false;
            }
            
            std::string cmdline;
            std::getline(cmdlineFile, cmdline);
            cmdlineFile.close();
            
            if (cmdline.empty()) {
                return false;
            }
            
            // Kiểm tra các debugger hợp lệ
            const char* legitimateDebuggers[] = {
                getObfuscatedString(1),  // gdb
                getObfuscatedString(2),  // lldb
                getObfuscatedString(3),  // strace
                getObfuscatedString(4),  // android_studio
                getObfuscatedString(5)   // idea
            };
            
            for (int i = 0; i < 5; i++) {
                if (cmdline.find(legitimateDebuggers[i]) != std::string::npos) {
                    return true;
                }
            }
            
            return false;
        } catch (...) {
            return false;
        }
    }
    
    // Kiểm tra đặc điểm của Game Guardian
    static bool checkGameGuardianCharacteristics(int pid) {
        try {
            // Kiểm tra memory maps
            std::string mapsPath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(11);
            std::ifstream mapsFile(mapsPath.c_str());
            
            if (mapsFile.is_open()) {
                std::string line;
                while (std::getline(mapsFile, line)) {
                    // Tìm signature của Game Guardian
                    if (line.find(getObfuscatedString(6)) != std::string::npos ||  // libgg.so
                        line.find(getObfuscatedString(7)) != std::string::npos ||  // GameGuardian
                        line.find(getObfuscatedString(8)) != std::string::npos) {  // game_cih
                        mapsFile.close();
                        return true;
                    }
                }
                mapsFile.close();
            }
            
            // Kiểm tra file descriptors
            std::string fdPath = getObfuscatedString(9) + std::to_string(pid) + getObfuscatedString(12);
            DIR* fdDir = opendir(fdPath.c_str());
            
            if (fdDir != nullptr) {
                int memoryAccessCount = 0;
                struct dirent* entry;
                
                while ((entry = readdir(fdDir)) != nullptr) {
                    if (entry->d_name[0] == '.') continue;
                    
                    std::string fdLinkPath = fdPath + "/" + entry->d_name;
                    char linkTarget[256];
                    ssize_t linkLen = readlink(fdLinkPath.c_str(), linkTarget, sizeof(linkTarget) - 1);
                    
                    if (linkLen > 0) {
                        linkTarget[linkLen] = '\0';
                        std::string target(linkTarget);
                        
                        // Game Guardian thường mở nhiều /proc/*/mem files
                        if (target.find(getObfuscatedString(9)) != std::string::npos && 
                            target.find(getObfuscatedString(13)) != std::string::npos) {
                            memoryAccessCount++;
                        }
                    }
                }
                closedir(fdDir);
                
                // Game Guardian thường mở > 2 memory access FDs
                if (memoryAccessCount > 2) {
                    return true;
                }
            }
            
            return false;
        } catch (...) {
            return true; // Lỗi = nghi ngờ
        }
    }

public:
    // 🛡️ CORE DETECTION METHOD
    static int detectGameGuardianCore() {
        try {
            std::string statusPath = getObfuscatedString(9);
            statusPath += "self";
            statusPath += getObfuscatedString(14);
            
            std::ifstream statusFile(statusPath.c_str());
            if (!statusFile.is_open()) {
                return 50; // Không đọc được = nghi ngờ
            }
            
            std::string line;
            while (std::getline(statusFile, line)) {
                if (line.substr(0, 9) == getObfuscatedString(0)) { // TracerPid:
                    std::string pidStr = line.substr(10);
                    
                    // Loại bỏ whitespace
                    pidStr.erase(0, pidStr.find_first_not_of(" \t"));
                    pidStr.erase(pidStr.find_last_not_of(" \t") + 1);
                    
                    int tracerPid = std::atoi(pidStr.c_str());
                    statusFile.close();
                    
                    if (tracerPid != 0) {
                        // Có process đang trace
                        if (!isValidDebugger(tracerPid)) {
                            if (checkGameGuardianCharacteristics(tracerPid)) {
                                return 95; // High confidence - Game Guardian detected
                            }
                            return 75; // Medium confidence - Suspicious tracer
                        }
                        return 10; // Low confidence - Valid debugger
                    }
                    return 0; // No tracer
                }
            }
            statusFile.close();
            return 0; // No TracerPid found
        } catch (...) {
            return 60; // Exception = suspicious
        }
    }
    
    // 🛡️ MEMORY PROTECTION TEST
    static bool detectMemoryTampering() {
        try {
            const uint32_t MAGIC_VALUE = 0x12345678;
            volatile uint32_t testValue = MAGIC_VALUE;
            
            // Monitor trong 5 giây
            for (int i = 0; i < 100; i++) {
                if (testValue != MAGIC_VALUE) {
                    return true; // Memory bị tamper
                }
                usleep(50000); // 50ms
            }
            
            return false; // Không có tampering
        } catch (...) {
            return false; // Lỗi = không detect được
        }
    }
    
    // 🛡️ ANTI-DEBUGGING SETUP
    static int setupAntiDebug() {
        try {
            // Self-trace để ngăn attachment
            long result = ptrace(PTRACE_TRACEME, 0, 0, 0);
            if (result == -1) {
                return 1; // Đã bị trace = có Game Guardian
            }
            return 0; // OK
        } catch (...) {
            return 1; // Lỗi = nghi ngờ
        }
    }
    
    // 🛡️ PROCESS ENUMERATION CHECK
    static bool detectSuspiciousProcesses() {
        try {
            DIR* procDir = opendir("/proc");
            if (procDir == nullptr) {
                return false;
            }
            
            struct dirent* entry;
            int suspiciousCount = 0;
            
            while ((entry = readdir(procDir)) != nullptr) {
                // Chỉ check numeric directories (PIDs)
                if (!isdigit(entry->d_name[0])) continue;
                
                std::string cmdlinePath = "/proc/";
                cmdlinePath += entry->d_name;
                cmdlinePath += "/cmdline";
                
                std::ifstream cmdlineFile(cmdlinePath.c_str());
                if (cmdlineFile.is_open()) {
                    std::string cmdline;
                    std::getline(cmdlineFile, cmdline);
                    cmdlineFile.close();
                    
                    // Tìm process names nghi ngờ
                    if (cmdline.length() <= 3 ||  // Tên quá ngắn
                        cmdline.find("gg") != std::string::npos ||
                        cmdline.find("cih") != std::string::npos) {
                        suspiciousCount++;
                    }
                }
            }
            closedir(procDir);
            
            return suspiciousCount > 2; // > 2 process nghi ngờ
        } catch (...) {
            return false;
        }
    }
};

// 🛡️ JNI EXPORTS - Compatible với AIDE CMOD
extern "C" {
    
JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeDetectGameGuardian(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectGameGuardianCore();
}

JNIEXPORT jboolean JNICALL  
Java_com_hmod_vip_Main_nativeDetectMemoryTampering(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectMemoryTampering();
}

JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeSetupAntiDebug(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::setupAntiDebug();
}

JNIEXPORT jboolean JNICALL
Java_com_hmod_vip_Main_nativeDetectSuspiciousProcesses(JNIEnv *env, jclass clazz) {
    return GameGuardianDetector::detectSuspiciousProcesses();
}

} // extern "C"
