#ifndef GAME_GUARDIAN_DETECTOR_H
#define GAME_GUARDIAN_DETECTOR_H

#include <jni.h>

#ifdef __cplusplus
extern "C" {
#endif

// 🛡️ GAME GUARDIAN DETECTION FUNCTIONS
// Compatible với AIDE CMOD

/**
 * <PERSON><PERSON><PERSON> hiện Game Guardian bằng TracerPid detection
 * @return confidence level (0-100)
 */
JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeDetectGameGuardian(JNIEnv *env, jclass clazz);

/**
 * Phát hiện memory tampering
 * @return true nếu phát hiện tampering
 */
JNIEXPORT jboolean JNICALL  
Java_com_hmod_vip_Main_nativeDetectMemoryTampering(JNIEnv *env, jclass clazz);

/**
 * Setup anti-debug protection
 * @return 0 nếu OK, 1 nếu đã bị trace
 */
JNIEXPORT jint JNICALL
Java_com_hmod_vip_Main_nativeSetupAntiDebug(JNIEnv *env, jclass clazz);

/**
 * Ph<PERSON>t hiện suspicious processes
 * @return true nếu phát hiện process nghi ngờ
 */
JNIEXPORT jboolean JNICALL
Java_com_hmod_vip_Main_nativeDetectSuspiciousProcesses(JNIEnv *env, jclass clazz);

#ifdef __cplusplus
}
#endif

#endif // GAME_GUARDIAN_DETECTOR_H
