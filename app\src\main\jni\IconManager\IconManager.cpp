#include "IconManager.h"
#include <fstream>
#include <sys/stat.h>
#include <unistd.h>
#include <dirent.h>

#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, "IconManager", __VA_ARGS__)


#define OBFUSCATE_URL(str) []() { \
    constexpr const char key = 0x55; \
    static const std::string encoded = []() { \
        std::string s = str; \
        for (char& c : s) c ^= key; \
        return s; \
    }(); \
    std::string decoded = encoded; \
    for (char& c : decoded) c ^= key; \
    return decoded; \
}()

size_t IconManager::WriteCallback(void* content, size_t size, size_t nmemb, void* userdata) {
    std::string* buffer = static_cast<std::string*>(userdata);
    size_t totalSize = size * nmemb;
    buffer->append((char*)content, totalSize);
    return totalSize;
}

bool IconManager::createDirectory(const std::string& path) {
    mkdir(path.c_str(), 0777);
    return true;
}


static std::string GetCurrentPackageName() {
    char application_id[256];
    FILE *fp = fopen("/proc/self/cmdline", "r");
    if (fp) {
        fread(application_id, sizeof(application_id), 1, fp);
        fclose(fp);
    }
    return std::string(application_id);
}

bool IconManager::downloadAndExtractIcons(const std::string& url, const std::string& packageName) {
    try {
        // Download file ZIP
        CURL* curl = curl_easy_init();
        if (!curl) return false;

        std::string zipData;
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &zipData);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);

        CURLcode res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);

        if (res != CURLE_OK) return false;

        // Tạo thư mục nếu chưa có
        std::string basePath = "/storage/emulated/0/Android/data/" + packageName + "/files/TH/";
        createDirectory(basePath);

        // Lưu file zip tạm
        std::string zipPath = basePath + "anogs.zip";
        std::ofstream zipFile(zipPath, std::ios::binary);
        zipFile.write(zipData.data(), zipData.size());
        zipFile.close();

        // Giải nén
        std::string cmd = "unzip -o " + zipPath + " -d " + basePath;
        system(cmd.c_str());

        // Xóa file zip tạm
        unlink(zipPath.c_str());

        return true;
    } catch (...) {
        return false;
    }
}

void InitializeIcons() {
    std::string packageName = GetCurrentPackageName();
    
    std::string url = OBFUSCATE_URL("https://hmod.io.vn/public/files/icon.zip");
    IconManager::downloadAndExtractIcons(url, packageName);
}
