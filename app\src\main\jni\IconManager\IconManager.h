#pragma once
#include <string>
#include <curl/curl.h>
#include <android/log.h>

class IconManager {
private:
    static size_t WriteCallback(void* content, size_t size, size_t nmemb, void* userdata);
    static bool createDirectory(const std::string& path);
    static bool fileExists(const std::string& path);
    static bool downloadFile(const std::string& url, std::string& outBuffer);

public:
    static bool downloadAndExtractIcons(const std::string& url, const std::string& packageName);
    static bool checkIconsExist(const std::string& packageName);
};

void InitializeIcons();
