#pragma once
#include <android/input.h>
#include <map>
#include <queue>
#include <cstdint>
#include <vector>
#include <map>
#include <android/input.h>
#include <android/keycodes.h>
#include <queue>
#include <cmath>
#include <limits>
#include <mutex>

// Forward declaration for EnemyData struct (defined in Overlay.h)
struct EnemyData;

// Include Overlay.h for full EnemyData definition when needed
// Note: This will be included after other necessary headers in Call_Hacks.h

ImVec2 menuRectSize; // Lấy kích thước của menu
ImVec2 menuRectPos;    // Lấy vị trí của menu hiện tại trong cửa sổ
bool isCaps;
std::vector<int> kdata;

bool isInsideRect(ImVec2 point, ImVec2 rectMin, ImVec2 rectMax) {
    return (point.x >= rectMin.x && point.x <= rectMax.x &&
            point.y >= rectMin.y && point.y <= rectMax.y);
}


void (*origInput)(...);
void myInput(void *inputConsumer, void *motionEvent, const void *inputMessage) {
    origInput(inputConsumer, motionEvent, inputMessage);
    ImGui_ImplAndroid_HandleInputEvent((AInputEvent *)inputConsumer, {(float) screenWidth / (float) glWidth, (float) screenHeight / (float) glHeight});
    return;
}



int32_t (*orig_ANativeWindow_getWidth)(ANativeWindow* window);
int32_t _ANativeWindow_getWidth(ANativeWindow* window) {
    screenWidth = orig_ANativeWindow_getWidth(window);
    return orig_ANativeWindow_getWidth(window);
}

int32_t (*orig_ANativeWindow_getHeight)(ANativeWindow* window);
int32_t _ANativeWindow_getHeight(ANativeWindow* window) {
    screenHeight = orig_ANativeWindow_getHeight(window);
    return orig_ANativeWindow_getHeight(window);
}

int32_t (*orig_AConfiguration_getDensity)(ANativeWindow* config);
int32_t _AConfiguration_getDensity(ANativeWindow* config) {
    density = orig_AConfiguration_getDensity(config);
    return orig_AConfiguration_getDensity(config);
}


jboolean (*orig_unity__nativeInjectEvent)(JNIEnv *env, jobject object, jobject inputEvent);
jboolean unity_nativeInjectEvent(JNIEnv *env, jobject object, jobject inputEvent) {
    ImGuiIO &io = ImGui::GetIO();
    
    jclass motionEventClass = env->FindClass(OBFUSCATE("android/view/MotionEvent"));
    
    if (env->IsInstanceOf(inputEvent, motionEventClass)) {
        
        
        }
    
    
    
    jclass KeyEventClass = env->FindClass(OBFUSCATE("android/view/KeyEvent"));
    if (env->IsInstanceOf(inputEvent, KeyEventClass)) {
        jmethodID getActionMethod = env->GetMethodID(KeyEventClass, OBFUSCATE("getAction"), OBFUSCATE("()I"));
        if (env->CallIntMethod(inputEvent, getActionMethod) == 0) {
            jmethodID getKeyCodeMethod = env->GetMethodID(KeyEventClass, OBFUSCATE("getKeyCode"), OBFUSCATE("()I"));
            jmethodID getUnicodeCharMethod = env->GetMethodID(KeyEventClass, OBFUSCATE("getUnicodeChar"), OBFUSCATE("(I)I"));
            jmethodID getMetaStateMethod = env->GetMethodID(KeyEventClass, OBFUSCATE("getMetaState"), OBFUSCATE("()I"));
            
            jmethodID getCharacters = env->GetMethodID(KeyEventClass, OBFUSCATE("getCharacters"), OBFUSCATE("()Ljava/lang/String;"));
            
            jint keyCode = env->CallIntMethod(inputEvent, getKeyCodeMethod);
            
            jint metaState = env->CallIntMethod(inputEvent, getMetaStateMethod);
            
            jstring characters =  (jstring)env->CallObjectMethod(inputEvent, getCharacters);
            
            jint unicodeChar = env->CallIntMethod(inputEvent, getUnicodeCharMethod, metaState);
            
            kdata.push_back(unicodeChar);
            
            ImGui_ImplAndroid_Addkeyboard(keyCode, unicodeChar);
            
            
            
        }
    }

    return orig_unity__nativeInjectEvent(env, object, inputEvent);
}



jint (*old_RegisterNatives)(JNIEnv*, jclass, const JNINativeMethod*, jint);
jint hook_RegisterNatives(JNIEnv* env, jclass klazz, const JNINativeMethod* methods, jint methodcount) {
    for (int i = 0; i < methodcount; ++i) {
        auto method = methods[i];
        
        if (strcmp(method.name, OBFUSCATE("nativeInjectEvent")) == 0) {
            Tools::Hook((void *) method.fnPtr, (void *) unity_nativeInjectEvent, (void **) &orig_unity__nativeInjectEvent);
        }
    }
    return old_RegisterNatives(env, klazz, methods, methodcount);
}
float (*old_GetCameraHeightRateValue)(void *instance, int *type);
float GetCameraHeightRateValue(void *instance, int *type) {
    if (instance != NULL) {
        Config.WideView.GetFieldOfView = old_GetCameraHeightRateValue(instance, type);
        if (Config.WideView.SetFieldOfView != 0) {
            return (float) Config.WideView.SetFieldOfView + Config.WideView.GetFieldOfView;
        }
        return Config.WideView.GetFieldOfView;
    }
    return old_GetCameraHeightRateValue(instance, type);
}

void (*OnCameraHeightChanged)(void *instance);
void (*old_CameraSystemUpdate)(void *instance);
void CameraSystemUpdate(void *instance) {
    if (instance != NULL && Config.WideView.Active) {
        OnCameraHeightChanged(instance);
    }
    old_CameraSystemUpdate(instance);
}



bool (*old_get_Supported60FPSMode)(void *instance);
bool get_Supported60FPSMode(void *instance) {
  //  if (instance != NULL && FPS60) 
    {
        return true;
    }
    return old_get_Supported60FPSMode(instance);

}

bool (*old_AutoLogin)(void *instance);
bool AutoLogin(void *instance) {
    {
        return false;
    }
    return old_AutoLogin(instance);

}



extern bool AimElsu;
extern int aimType;

// Forward declaration for Config
struct sConfig;
extern sConfig Config;


// ==================== AIM SYSTEM INTEGRATION ====================

// Aim system macros for cleaner code
#define OBFUSCATE_METHOD(image, namespaze, clazz, name, args) \
IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name), args)

#define OBFUSCATE_FIELD(image, namespaze, clazz, name) \
IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name))

// Aim system classes and structures
class AimCamera {
    public:
        static AimCamera *get_main() {
            AimCamera *(*get_main_) () = (AimCamera *(*)())OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
            return get_main_();
        }

        Vector3 WorldToScreenPoint(Vector3 position) {
            Vector3 (*WorldToScreenPoint_)(AimCamera *camera, Vector3 position) = (Vector3 (*)(AimCamera *, Vector3))OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);
            return WorldToScreenPoint_(this, position);
        }
};

class AimValueLinkerComponent {
    public:
        int get_actorHp() {
            int (*get_actorHp_)(AimValueLinkerComponent * objLinkerWrapper) = (int (*)(AimValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHp", 0);
            return get_actorHp_(this);
        }

        int get_actorHpTotal() {
            int (*get_actorHpTotal_)(AimValueLinkerComponent * objLinkerWrapper) =
                (int (*)(AimValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHpTotal", 0);
            return get_actorHpTotal_(this);
        }
};

class AimActorConfig {
    public:
        int ConfigID() {
            return *(int *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "ActorConfig", "ConfigID"));
        }
};

class AimActorLinker {
    public:
        AimValueLinkerComponent *ValueComponent() {
            return *(AimValueLinkerComponent **)((uintptr_t)this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ValueComponent"));
        }

        AimActorConfig *ObjLinker() {
            return *(AimActorConfig **) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ObjLinker"));
        }

        Vector3 get_position() {
            Vector3 (*get_position_)(AimActorLinker * linker) = (Vector3(*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_position", 0);
            return get_position_(this);
        }

        Quaternion get_rotation() {
            Quaternion (*get_rotation_)(AimActorLinker *linker) = (Quaternion (*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_rotation", 0);
            return get_rotation_(this);
        }

        bool IsHostCamp() {
            bool (*IsHostCamp_)(AimActorLinker *linker) = (bool (*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostCamp", 0);
            return IsHostCamp_(this);
        }

        bool IsHostPlayer() {
            bool (*IsHostPlayer_)(AimActorLinker *linker) = (bool (*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostPlayer", 0);
            return IsHostPlayer_(this);
        }

        bool isMoving() {
            return *(bool *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "isMoving"));
        }

        Vector3 get_logicMoveForward() {
            Vector3 (*get_logicMoveForward_)(AimActorLinker *linker) = (Vector3 (*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_logicMoveForward", 0);
            return get_logicMoveForward_(this);
        }

        bool get_bVisible() {
            bool (*get_bVisible_)(AimActorLinker *linker) = (bool (*)(AimActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_bVisible", 0);
            return get_bVisible_(this);
        }
};

class AimActorManager {
    public:
        List<AimActorLinker *> *GetAllHeros() {
            List<AimActorLinker *> *(*_GetAllHeros)(AimActorManager *actorManager) = (List<AimActorLinker *> *(*)(AimActorManager *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorManager", "GetAllHeros", 0);
            return _GetAllHeros(this);
        }
};

class AimKyriosFramework {
    public:
        static AimActorManager *get_actorManager() {
            auto get_actorManager_ = (AimActorManager *(*)())OBFUSCATE_METHOD("Project_d.dll", "Kyrios", "KyriosFramework", "get_actorManager", 0);
            return get_actorManager_();
        }
};



// Aim system global variables (extern declarations)
extern AimEntityInfo AimEnemyTarget;
extern bool aimIsCharging;
extern int aimMode, aimDrawType, aimSkillSlot;

// Forward declarations for ESP integration
extern std::vector<EnemyData> enemyCache;
extern std::mutex cacheMutex;

// Enhanced aim system utility functions
Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.a, x = q.b, y = q.c, z = q.d;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

// NEW: Enhanced aim configuration (from Config.Feat)
extern bool aimIgnoreInvisible; // Option to ignore invisible enemies
extern bool aimUseESPData;      // Use ESP data for targeting
extern bool aimPredictMovement; // Predict enemy movement
extern float aimSmoothness;     // Aim smoothness factor
extern bool aimShowDebugInfo;   // Show debug information
extern bool aimShowTargetLine;  // Show line to current aim target
extern float aimLineThickness;  // Thickness of aim line

// NEW: Get enemies from ESP cache (more reliable than GetAllHeros)
std::vector<EnemyData> GetEnemiesFromESP(bool includeInvisible = true) {
    std::vector<EnemyData> enemies;

    // Check if ESP data should be used
    if (!aimUseESPData) {
        return enemies; // Return empty if ESP data is disabled
    }

    try {
        std::lock_guard<std::mutex> lock(cacheMutex);

        // Filter valid enemies for aim system
        for (const auto& enemy : enemyCache) {
            // Skip dead enemies
            if (enemy.isDead || enemy.hp <= 0) {
                continue;
            }

            // Skip if no valid objects
            if (!enemy.enemyObj || !enemy.enemyLinker) {
                continue;
            }

            // ENHANCED: Option to skip invisible enemies (from config)
            if (!includeInvisible && !enemy.isVisible) {
                continue;
            }

            // Add to aim candidates
            enemies.push_back(enemy);
        }
    } catch (...) {
        // Return empty vector on error
        enemies.clear();
    }

    return enemies;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.x - o.x) * (v.x - o.x) + (v.y - o.y) * (v.y - o.y) + (v.z - o.z) * (v.z - o.z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {enemyPosi += moveForward;}
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

float getAimRange(int configID) {
    switch(configID) {
        case 196: return 25.f;  // Elsu
        case 108: return 13.f;  // Other heroes
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}

// NEW: Enhanced target selection using ESP data
EnemyData* SelectBestTarget(const std::vector<EnemyData>& enemies, Vector3 myPos, int myConfigID, Quaternion rotation) {
    if (enemies.empty()) {
        return nullptr;
    }

    float minDistance = std::numeric_limits<float>::infinity();
    float minDirection = std::numeric_limits<float>::infinity();
    float minHealth = std::numeric_limits<float>::infinity();
    float minHealth2 = std::numeric_limits<float>::infinity();
    float minHealthPercent = std::numeric_limits<float>::infinity();

    EnemyData* bestTarget = nullptr;
    float aimRange = getAimRange(myConfigID);

    for (const auto& enemy : enemies) {
        Vector3 enemyPos = enemy.position;
        float health = (float)enemy.hp;
        float maxHealth = (float)enemy.maxHp;
        float distance = Vector3::Distance(myPos, enemyPos);

        // Skip if out of range
        if (distance > aimRange) {
            continue;
        }

        // Calculate health percentage
        int healthPercent = (maxHealth > 0) ? (int)std::round(health / maxHealth * 100) : 0;

        // Calculate direction for crosshair aim
        Vector3 direction = calculateSkillDirection(myPos, enemyPos, false, Vector3::zero());
        float directionScore = SquaredDistance(RotateVectorByQuaternion(rotation), direction);

        // Apply target selection logic based on aimType
        bool isNewBest = false;

        if (aimType == 0) {  // % Máu thấp nhất
            if (healthPercent < minHealthPercent) {
                isNewBest = true;
                minHealthPercent = healthPercent;
            } else if (healthPercent == minHealthPercent && health < minHealth2) {
                isNewBest = true;
                minHealth2 = health;
            }
        } else if (aimType == 1 && health < minHealth) {  // Máu thấp nhất
            isNewBest = true;
            minHealth = health;
        } else if (aimType == 2 && distance < minDistance) {  // Khoảng cách gần nhất
            isNewBest = true;
            minDistance = distance;
        } else if (aimType == 3 && directionScore < minDirection && aimIsCharging) {  // Hướng tối ưu
            isNewBest = true;
            minDirection = directionScore;
        }

        if (isNewBest) {
            bestTarget = const_cast<EnemyData*>(&enemy);
        }
    }

    return bestTarget;
}

// Aim system hook functions
Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse);
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != NULL && AimElsu) {
        if (AimEnemyTarget.ConfigID == 196 || AimEnemyTarget.ConfigID == 108 || AimEnemyTarget.ConfigID == 157 || AimEnemyTarget.ConfigID == 175 || AimEnemyTarget.ConfigID == 545) {
            if (AimEnemyTarget.myPos != Vector3::zero() && AimEnemyTarget.enemyPos != Vector3::zero() && aimSkillSlot == 2) {
                return calculateSkillDirection(AimEnemyTarget.myPos, AimEnemyTarget.enemyPos, AimEnemyTarget.isMoving, AimEnemyTarget.moveForward);
            }
        }
    }
    return _GetUseSkillDirection(instance, isTouchUse);
}

void (*_AimUpdateLogic)(void *instance, int delta);
void AimUpdateLogic(void *instance, int delta) {
     if (instance != NULL) {
        aimIsCharging = *(bool *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_isCharging"));
        aimSkillSlot = *(int *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_currentSkillSlotType"));
    }

    if (AimElsu) {
        // ENHANCED: Sync config values from Config.FEATMenu
        aimUseESPData = Config.FEATMenu.aimUseESPData;
        aimIgnoreInvisible = Config.FEATMenu.aimIgnoreInvisible;
        aimPredictMovement = Config.FEATMenu.aimPredictMovement;
        aimSmoothness = Config.FEATMenu.aimSmoothness;
        aimShowDebugInfo = Config.FEATMenu.aimShowDebugInfo;
        aimShowTargetLine = Config.FEATMenu.aimShowTargetLine;
        aimLineThickness = Config.FEATMenu.aimLineThickness;

        // ENHANCED: Use ESP data instead of GetAllHeros for better accuracy
        // Option to include/exclude invisible enemies based on config
        std::vector<EnemyData> espEnemies = GetEnemiesFromESP(!aimIgnoreInvisible);

        // Debug info if enabled
        if (aimShowDebugInfo && !espEnemies.empty()) {
            LOGD("AIM DEBUG: Found %d valid enemies from ESP", (int)espEnemies.size());
        }

        // Get player info from ActorManager (still needed for player data)
        AimActorManager *get_actorManager = AimKyriosFramework::get_actorManager();
        if (get_actorManager == nullptr) {
            _AimUpdateLogic(instance, delta);
            return;
        }

        List<AimActorLinker *> *GetAllHeros = get_actorManager->GetAllHeros();
        if (GetAllHeros == nullptr) {
            _AimUpdateLogic(instance, delta);
            return;
        }

        AimActorLinker **actorLinkers = (AimActorLinker **) GetAllHeros->getItems();

        // Get player data (rotation, position, configID)
        Quaternion rotation;
        Vector3 myPos = Vector3::zero();
        int myConfigID = 0;

        // Get player data from ActorManager
        for (int i = 0; i < GetAllHeros->getSize(); i++) {
            AimActorLinker *actorLinker = actorLinkers[(i *2) + 1];
            if (actorLinker == nullptr) continue;

            if (actorLinker->IsHostPlayer()) {
                rotation = actorLinker->get_rotation();
                myPos = actorLinker->get_position();
                myConfigID = actorLinker->ObjLinker()->ConfigID();

                // Update global aim target info
                AimEnemyTarget.myPos = myPos;
                AimEnemyTarget.ConfigID = myConfigID;
                break; // Found player, no need to continue
            }
        }

        // ENHANCED: Use ESP data for enemy selection (more reliable)
        EnemyData* bestTarget = SelectBestTarget(espEnemies, myPos, myConfigID, rotation);

        // Update AimEnemyTarget based on ESP data
        if (bestTarget != nullptr) {
            AimEnemyTarget.enemyPos = bestTarget->position;
            AimEnemyTarget.isMoving = false; // Default to static
            AimEnemyTarget.moveForward = Vector3::zero(); // Default to no movement

            // ENHANCED: Get movement data from ActorLinker if movement prediction is enabled
            if (aimPredictMovement) {
                try {
                    if (bestTarget->enemyLinker) {
                        AimActorLinker* enemyLinker = (AimActorLinker*)bestTarget->enemyLinker;
                        if (enemyLinker) {
                            AimEnemyTarget.moveForward = enemyLinker->get_logicMoveForward();
                            AimEnemyTarget.isMoving = enemyLinker->isMoving();

                            // Apply smoothness factor to movement prediction
                            if (AimEnemyTarget.isMoving && aimSmoothness > 0.0f) {
                                AimEnemyTarget.moveForward = AimEnemyTarget.moveForward * aimSmoothness;
                            }

                            if (aimShowDebugInfo && AimEnemyTarget.isMoving) {
                                LOGD("AIM DEBUG: Target moving, forward vector: (%.2f, %.2f, %.2f)",
                                     AimEnemyTarget.moveForward.x, AimEnemyTarget.moveForward.y, AimEnemyTarget.moveForward.z);
                            }
                        }
                    }
                } catch (...) {
                    // Fallback to static targeting if movement data unavailable
                    AimEnemyTarget.isMoving = false;
                    AimEnemyTarget.moveForward = Vector3::zero();
                    if (aimShowDebugInfo) {
                        LOGD("AIM DEBUG: Failed to get movement data, using static targeting");
                    }
                }
            }
        } else {
            // No valid target found
            AimEnemyTarget.enemyPos = Vector3::zero();
            AimEnemyTarget.moveForward = Vector3::zero();
            AimEnemyTarget.isMoving = false;

            if (aimShowDebugInfo) {
                LOGD("AIM DEBUG: No valid target found from %d ESP enemies", (int)espEnemies.size());
            }
        }

        // ENHANCED: Additional validation for ESP-based targeting
        if (bestTarget != nullptr && AimEnemyTarget.enemyPos != Vector3::zero()) {
            // Validate target is still in range and alive
            float targetDistance = Vector3::Distance(AimEnemyTarget.myPos, AimEnemyTarget.enemyPos);
            float aimRange = getAimRange(AimEnemyTarget.ConfigID);

            if (targetDistance > aimRange || bestTarget->hp <= 0 || bestTarget->isDead) {
                // Target is no longer valid
                AimEnemyTarget.enemyPos = Vector3::zero();
                AimEnemyTarget.moveForward = Vector3::zero();
                AimEnemyTarget.isMoving = false;
                bestTarget = nullptr;
            }
        }

        // Special handling for crosshair aim mode (type 3)
        if (bestTarget != nullptr && aimType == 3 && !aimIsCharging) {
            // Only aim when charging for crosshair mode
            AimEnemyTarget.enemyPos = Vector3::zero();
            AimEnemyTarget.moveForward = Vector3::zero();
            AimEnemyTarget.isMoving = false;
            bestTarget = nullptr;
        }

        if (aimDrawType != 0 && (AimEnemyTarget.ConfigID == 196 || AimEnemyTarget.ConfigID == 108 || AimEnemyTarget.ConfigID == 157 || AimEnemyTarget.ConfigID == 175 || AimEnemyTarget.ConfigID == 545)) {
            if (AimEnemyTarget.myPos != Vector3::zero() && AimEnemyTarget.enemyPos != Vector3::zero()) {
                Vector3 EnemySC = AimCamera::get_main()->WorldToScreenPoint(AimEnemyTarget.enemyPos);
                if (EnemySC.z > 0) {
                    // Visual feedback can be added here if needed
                }
            }
        }
    }

    _AimUpdateLogic(instance, delta);
}





