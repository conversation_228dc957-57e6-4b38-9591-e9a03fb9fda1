#pragma once

#include <map>
#include <mutex>
#include <chrono>
#include <vector>
#include <fstream>
#include <string>
#include <algorithm> // For std::find

// Cấu trúc lưu trữ dữ liệu kẻ địch cho ESP và Aim
struct EnemyData {
    void* enemyObj;        // LActorRoot object
    void* enemyLinker;     // ActorLinker object
    Vector3 position;      // Vị trí chính xác từ ESP
    int hp;               // HP hiện tại
    int maxHp;            // HP tối đa
    bool isVisible;       // Trạng thái hiển thị
    bool isDead;          // Trạng thái chết
    int configId;         // ID cấu hình tướng
    std::string heroName; // Tên tướng
};

// Managers
ESPManager *espManager;
ESPManager *ActorLinker_enemy;

// Global variables for tracking
Array<void *> *m_MiniMapHeroInfos;
std::map<void *, void *> player_mini;
std::map<string, GLuint> mapIcon;
std::vector<GLuint> enemyIconTextures;
std::vector<String*> enemyNames;
std::map<int, std::string> objIdToHeroIdMap; // Ánh xạ ObjID -> HeroID
std::map<void*, std::string> enemyPtrToHeroIdMap; // Ánh xạ Enemy Pointer -> HeroID (stable)
std::map<void*, GLuint> enemyPtrToTextureMap; // Ánh xạ Enemy Pointer -> Texture ID
std::map<std::string, GLuint> heroIdToTextureMap; // Cache texture theo HeroID

// State tracking variables
bool hasInitializedIcons = false;
static int currentMatchId = 0;
static void* lastMyPlayer = nullptr;
static std::map<int, std::string> playerHeroCache;
static int lastEnemyCount = 0;
static float lastUpdateTime = 0;
static int lastMatchId = -1;
static bool forceUpdate = false;

// CRITICAL FIX: Backup mapping system for when GetHero_Icon fails
static std::map<void*, std::string> backupEnemyMapping;
static std::map<void*, int> enemySpawnOrder;
static int globalSpawnCounter = 0;
float mapNgh = 1.735f;
float radiusMini = 10;
int mimapi = 0;
float scaleX;

// Performance optimization variables
static const float ESP_UPDATE_INTERVAL = 0.0f; // ~60 FPS
static const float ICON_UPDATE_INTERVAL = 2.0f; // Update icons every 2s - giảm nháy
static const int TEXTURE_COOLDOWN = 3000; // 3 seconds - giảm nháy màn hình
static std::mutex textureMutex;

// Cache dữ liệu kẻ địch
std::vector<EnemyData> enemyCache;
std::mutex cacheMutex;
float lastCacheUpdate = 0.0f; 
const float CACHE_UPDATE_INTERVAL = 0.05f; // 20 FPS

// Các biến thêm để xử lý trường hợp ESP được bật trước khi vào trận
bool espInitialized = false;
bool matchStarted = false;
std::mutex espStateMutex;
static int initializationAttempts = 0;
static const int MAX_INITIALIZATION_ATTEMPTS = 10;
static float lastInitAttemptTime = 0.0f;
static const float INIT_ATTEMPT_INTERVAL = 1.0f; // 1 giây giữa các lần thử khởi tạo

// ESP Simple Activation System - Enemy Count Based
static float matchStartTime = 0;
static bool isMatchActive = false;
static const float ESP_ACTIVATION_DELAY = 3.0f; // Simple 3 second delay
static bool espSafeToRun = false;

// Function declarations
void *hack_thread(void *);

// Actor Root functions
VInt3 (*LActorRoot_get_location)(void *instance);
VInt3 (*LActorRoot_get_forward)(void *instance);
uintptr_t (*LActorRoot_LHeroWrapper)(void *instance);
int (*LActorRoot_COM_PLAYERCAMP)(void *instance);
bool (*LActorRoot_get_bActive)(void *instance);
int (*LActorRoot_get_ObjID)(void *instance);

// Object Wrapper functions
bool (*LObjWrapper_get_IsDeadState)(void *instance);
bool (*LObjWrapper_IsAutoAI)(void *instance);

// Value Property functions
int (*ValuePropertyComponent_get_actorHp)(void *instance);
int (*ValuePropertyComponent_get_actorHpTotal)(void *instance);
void* (*ValuePropertyComponent_BaseEnergyLogic)(void *instance);

// Actor Linker functions
int (*ActorLinker_COM_PLAYERCAMP)(void *instance);
bool (*ActorLinker_IsHostPlayer)(void *instance);
bool (*ActorLinker_IsHostCamp)(void *instance);
int (*ActorLinker_ActorTypeDef)(void *instance);
Vector3 (*ActorLinker_getPosition)(void *instance);
bool (*ActorLinker_get_HPBarVisible)(void *instance);
int (*ActorLinker_get_ObjID)(void *instance);
bool (*ActorLinker_get_bVisible)(void *instance);
uintptr_t (*AsHero)(...);

// MiniMap functions
int (*MiniMapHeroInfo_get_ObjID)(void *instance);
void (*MiniMapHeroInfo_Show)(void *instance, bool show);
String *(*GetHeroName)(int uuid);
void (*UpdatePosInMiniMap)(void *instance, Vector3 actopos);
String *(*GetHero_Icon)(void *instance, bool bSmall);

// AimEntityInfo struct definition (moved from Hooker.h to avoid circular dependency)
struct AimEntityInfo {
    Vector3 myPos;
    Vector3 enemyPos;
    Vector3 moveForward;
    int ConfigID;
    bool isMoving;
};

// Forward declarations for aim system integration
extern AimEntityInfo AimEnemyTarget;
extern bool aimShowTargetLine;
extern float aimLineThickness;

// Kiểm tra xem game đã sẵn sàng để ESP hoạt động chưa
bool IsGameReady() {
    static float lastLog = 0;
    float now = ImGui::GetTime();

    bool ready = espManager != NULL &&
                 espManager->enemies != NULL &&
                 ActorLinker_enemy != NULL &&
                 ActorLinker_enemy->enemies != NULL;

    if (!ready && now - lastLog > 3.0f) {
        LOGD("IsGameReady: espManager=%p, enemies=%p, ActorLinker_enemy=%p, ActorLinker_enemies=%p",
             espManager,
             espManager ? espManager->enemies : NULL,
             ActorLinker_enemy,
             ActorLinker_enemy ? ActorLinker_enemy->enemies : NULL);
        lastLog = now;
    }

    return ready;
}

// Kiểm tra sâu hơn xem trận đấu đã sẵn sàng hoàn toàn chưa
bool IsMatchReady() {
    return IsGameReady() &&
           espManager->MyPlayer != NULL &&
           !espManager->enemies->empty();
}

// Simple Enemy Count Check - Kiểm tra đơn giản dựa trên số lượng enemy
bool IsEnemyCountReady() {
    if (!espManager || !espManager->enemies) {
        return false;
    }

    int enemyCount = espManager->enemies->size();
    return enemyCount >= 1; // Cần ít nhất 1 enemy (hỗ trợ solo mode)
}



// Utility functions
bool textureExists(const std::string& textureName) {
    return mapIcon.find(textureName) != mapIcon.end();
}

bool fileExists(const std::string& filePath) {
    std::ifstream file(filePath);
    return file.good();
}

// Xóa bộ đệm texture an toàn
void ClearTextureCache() {
    try {
        std::lock_guard<std::mutex> lock(textureMutex);
        
        for(auto& pair : mapIcon) {
            if(pair.second != 0) {
                glDeleteTextures(1, &pair.second);
            }
        }
        mapIcon.clear();
        
        for(GLuint textureId : enemyIconTextures) {
            if(textureId != 0) {
                glDeleteTextures(1, &textureId);
            }
        }
        enemyIconTextures.clear();
        
        playerHeroCache.clear();
        enemyNames.clear();
        objIdToHeroIdMap.clear(); // Xóa mapping

        // Clear stable mappings
        enemyPtrToHeroIdMap.clear();
        enemyPtrToTextureMap.clear();

        // Clear hero texture cache
        for (auto& pair : heroIdToTextureMap) {
            if (pair.second != 0) {
                glDeleteTextures(1, &pair.second);
            }
        }
        heroIdToTextureMap.clear();

        hasInitializedIcons = false;
        forceUpdate = false;
        
        // Xóa cache
        std::lock_guard<std::mutex> cacheLock(cacheMutex);
        enemyCache.clear();
    } catch(...) {
        LOGD("Exception in ClearTextureCache");
    }
}

const char *getPack() {
    char *application_id[256];
    FILE *fp = fopen("proc/self/cmdline", "r");
    if (fp) {
        fread(application_id, sizeof(application_id), 1, fp);
        fclose(fp);
    }
    return (const char *) application_id;
}

int dem(int num) {
    int div = 1, num1 = num;
    while (num1 != 0) {
        num1 = num1/10;
        div = div*10;
    }
    return div;
}

Vector3 VInt2Vector(VInt3 location, VInt3 forward) {
    return Vector3(
        (float)(location.X*dem(forward.X)+forward.X)/(1000*dem(forward.X)),
        (float)(location.Y*dem(forward.Y)+forward.Y)/(1000*dem(forward.Y)),
        (float)(location.Z*dem(forward.Z)+forward.Z)/(1000*dem(forward.Z))
    );
}

// Cải tiến LoadTexture để an toàn hơn
GLuint LoadTexture(const char* imagePath) {
    if (!imagePath || strlen(imagePath) == 0) {
        LOGD("LoadTexture: Invalid image path");
        return 0;
    }
    
    try {
        std::lock_guard<std::mutex> lock(textureMutex);
        std::string pathStr(imagePath);
        
        static std::map<std::string, std::chrono::steady_clock::time_point> lastLoadTimes;
        auto now = std::chrono::steady_clock::now();
        
        // Check cooldown
        if(lastLoadTimes.find(pathStr) != lastLoadTimes.end()) {
            if(std::chrono::duration_cast<std::chrono::milliseconds>
               (now - lastLoadTimes[pathStr]).count() < TEXTURE_COOLDOWN) {
                if (textureExists(pathStr)) {
                    return mapIcon[pathStr];
                }
            }
        }
        
        // Handle force update - chỉ khi thực sự cần thiết
        static float lastForceUpdateTime = 0;
        float updateTime = ImGui::GetTime();

        if(forceUpdate && (updateTime - lastForceUpdateTime > 5.0f)) { // Chỉ force update mỗi 5 giây
            auto it = mapIcon.find(pathStr);
            if(it != mapIcon.end()) {
                if(it->second != 0) {
                    glDeleteTextures(1, &it->second);
                }
                mapIcon.erase(it);
            }
            lastForceUpdateTime = updateTime;
        }
        
        // Return cached texture if exists - ưu tiên cache để giảm nháy
        if (textureExists(pathStr)) {
            return mapIcon[pathStr];
        }
        
        // Check if file exists
        if (!fileExists(pathStr)) {
            LOGD("LoadTexture: File does not exist: %s", pathStr.c_str());
            return 0;
        }
        
        // Load new texture
        GLuint textureID = 0;
        glGenTextures(1, &textureID);
        
        if (textureID == 0) {
            LOGD("LoadTexture: Failed to generate texture");
            return 0;
        }
        
        int width, height, channels;
        stbi_set_flip_vertically_on_load(true);
        unsigned char *imageData = stbi_load(imagePath, &width, &height, &channels, STBI_rgb_alpha);
        
        if (imageData) {
            glBindTexture(GL_TEXTURE_2D, textureID);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
            glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
            glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, width, height, 0, GL_RGBA, GL_UNSIGNED_BYTE, imageData);
            glGenerateMipmap(GL_TEXTURE_2D);
            stbi_image_free(imageData);
            glBindTexture(GL_TEXTURE_2D, 0);
            
            mapIcon[pathStr] = textureID;
            lastLoadTimes[pathStr] = now;
        } else {
            LOGD("LoadTexture: Failed to load image data: %s", pathStr.c_str());
            glDeleteTextures(1, &textureID);
            textureID = 0;
        }
        
        return textureID;
    } catch(...) {
        LOGD("Exception in LoadTexture");
        return 0;
    }
}

// Tìm lỗi khi khởi tạo ESP
void InitializeESP() {
    float initTime = ImGui::GetTime();
    
    // Hạn chế số lần thử khởi tạo
    if (espInitialized ||
        (initializationAttempts >= MAX_INITIALIZATION_ATTEMPTS && !IsGameReady()) ||
        (initTime - lastInitAttemptTime < INIT_ATTEMPT_INTERVAL)) {
        return;
    }

    lastInitAttemptTime = initTime;
    initializationAttempts++;

    LOGD("Đang thử khởi tạo ESP, lần thử thứ %d", initializationAttempts);
    
    if (!IsGameReady()) {
        LOGD("ESP chưa sẵn sàng: espManager=%p, enemies=%p, ActorLinker_enemy=%p", 
              espManager, 
              espManager ? espManager->enemies : NULL, 
              ActorLinker_enemy);
        return;
    }
    
    try {
        LOGD("ESP đã sẵn sàng để khởi tạo");
        
        // Xóa dữ liệu cũ nếu có
        ClearTextureCache();
        
        // Khởi tạo bộ đệm rỗng
        std::lock_guard<std::mutex> cacheLock(cacheMutex);
        enemyCache.clear();
        
        espInitialized = true;
        initializationAttempts = 0; // Reset counter
        LOGD("ESP được khởi tạo thành công");
    } catch (const std::exception& e) {
        LOGD("Lỗi khởi tạo ESP: %s", e.what());
        espInitialized = false;
    } catch (...) {
        LOGD("Lỗi không xác định khi khởi tạo ESP");
        espInitialized = false;
    }
}

// Kiểm tra trận đấu mới - Đơn giản hóa
void CheckForNewMatch() {
    if (!espInitialized) {
        return;
    }

    try {
        // Kiểm tra espManager và enemies có tồn tại không
        if (!espManager || !espManager->enemies) {
            matchStarted = false;
            isMatchActive = false;
            return;
        }

        static int lastEnemyCount = 0;
        int currentEnemyCount = espManager->enemies->size();

        // Phát hiện bắt đầu trận đấu: từ 0 enemies lên >= 1 enemy (hỗ trợ solo mode)
        if (currentEnemyCount >= 1 && lastEnemyCount == 0) {
            matchStarted = true;
            isMatchActive = true;
            matchStartTime = ImGui::GetTime();
            espSafeToRun = false; // Reset để delay 3 giây

            LOGD("CRITICAL FIX: New match started! Enemy count: %d -> %d, performing complete reset",
                 lastEnemyCount, currentEnemyCount);

            // CRITICAL FIX: Complete reset of all mapping systems
            hasInitializedIcons = false;
            forceUpdate = true;

            // Clear all mappings
            enemyPtrToHeroIdMap.clear();
            objIdToHeroIdMap.clear();
            enemyPtrToTextureMap.clear();
            backupEnemyMapping.clear();
            enemySpawnOrder.clear();
            globalSpawnCounter = 0;

            // Clear icon arrays
            enemyIconTextures.clear();
            enemyNames.clear();

            // Xóa sạch dữ liệu cũ
            ClearTextureCache();

            LOGD("CRITICAL FIX: Complete reset finished for new match");
        }

        // Phát hiện kết thúc trận đấu: từ >= 1 enemy xuống 0 enemies
        if (currentEnemyCount == 0 && lastEnemyCount >= 1) {
            matchStarted = false;
            isMatchActive = false;
            matchStartTime = 0;
            espSafeToRun = false;

            LOGD("Match ended - Enemy count: %d -> %d", lastEnemyCount, currentEnemyCount);
            ClearTextureCache();
        }

        lastEnemyCount = currentEnemyCount;
    } catch(...) {
        LOGD("Exception in CheckForNewMatch");
    }
}

// Kiểm tra ESP có được phép hoạt động không - Đơn giản hóa
bool IsESPAllowedToRun() {
    if (!Config.ESPMenu.Enable_ESP) {
        return false;
    }

    if (!isMatchActive) {
        return false;
    }

    // Kiểm tra enemy count
    if (!IsEnemyCountReady()) {
        return false;
    }

    float currentTime = ImGui::GetTime();
    float timeSinceMatchStart = currentTime - matchStartTime;

    // Đơn giản: chỉ cần delay 3 giây sau khi có >= 1 enemy (hỗ trợ solo mode)
    if (!espSafeToRun && timeSinceMatchStart >= ESP_ACTIVATION_DELAY) {
        espSafeToRun = true;
        LOGD("ESP ACTIVATED after %.1f seconds with %d enemies!",
             timeSinceMatchStart, espManager->enemies->size());
    }

    return espSafeToRun;
}

// FAST & SIMPLE: Get Hero ID without performance killing delays
std::string SafeGetHeroId(void* enemyLinker) {
    if (!enemyLinker) {
        return "";
    }

    try {
        String* heroIcon = GetHero_Icon(enemyLinker, true);
        if (!heroIcon) {
            return "";
        }

        string iconPath = heroIcon->CString();
        if (iconPath.empty()) {
            return "";
        }

        size_t lastSlash = iconPath.rfind('/');
        if (lastSlash == std::string::npos) {
            return "";
        }

        std::string heroId = iconPath.substr(lastSlash + 1).substr(2, 3);

        // Simple validation: check format
        if (heroId.length() == 3) {
            bool isValidFormat = true;
            for (char c : heroId) {
                if (!std::isdigit(c)) {
                    isValidFormat = false;
                    break;
                }
            }

            if (isValidFormat) {
                return heroId; // Valid heroId found!
            }
        }

    } catch (...) {
        // Ignore errors, try backup method
    }

    return "";
}

// CRITICAL FIX: Backup Hero ID Detection using alternative methods
std::string GetBackupHeroId(void* enemyLinker) {
    if (!enemyLinker) {
        return "";
    }

    try {
        // Method 1: Try to get from spawn order và known hero patterns
        auto spawnIt = enemySpawnOrder.find(enemyLinker);
        if (spawnIt != enemySpawnOrder.end()) {
            int spawnOrder = spawnIt->second;

            // Common hero IDs in AOV (adjust based on current meta)
            std::vector<std::string> commonHeroes = {"101", "102", "103", "104", "105", "106", "107", "108", "109", "110"};
            if (spawnOrder < commonHeroes.size()) {
                LOGD("CRITICAL FIX: Using backup heroId %s for enemy %p (spawn order %d)",
                     commonHeroes[spawnOrder].c_str(), enemyLinker, spawnOrder);
                return commonHeroes[spawnOrder];
            }
        }

        // Method 2: Try to extract from memory patterns (if available)
        // This is a fallback method - implement based on game's memory structure

        // Method 3: Use objId as heroId (last resort)
        try {
            int objId = ActorLinker_get_ObjID(enemyLinker);
            if (objId > 0 && objId < 1000) {
                std::string heroId = std::to_string(objId % 200 + 101); // Map to valid hero range
                LOGD("CRITICAL FIX: Using objId-based heroId %s for enemy %p (objId: %d)",
                     heroId.c_str(), enemyLinker, objId);
                return heroId;
            }
        } catch(...) {
            // Ignore objId errors
        }

    } catch(...) {
        LOGD("CRITICAL FIX: Exception in GetBackupHeroId for enemy %p", enemyLinker);
    }

    return "";
}

// Safe Memory Access Wrappers - Truy cập memory an toàn
bool SafeGetEnemyPosition(void* enemyObject, Vector3& outPosition) {
    if (!enemyObject) {
        return false;
    }

    try {
        outPosition = ActorLinker_getPosition(enemyObject);
        return true;
    } catch (...) {
        return false;
    }
}

bool SafeGetEnemyActorType(void* enemyObject, int& outActorType) {
    if (!enemyObject) {
        return false;
    }

    try {
        outActorType = ActorLinker_ActorTypeDef(enemyObject);
        return true;
    } catch (...) {
        return false;
    }
}

bool SafeCheckIsHostPlayer(void* enemyObject, bool& outIsHost) {
    if (!enemyObject) {
        return false;
    }

    try {
        outIsHost = ActorLinker_IsHostPlayer(enemyObject);
        return true;
    } catch (...) {
        return false;
    }
}





// FAST & SIMPLE: Enemy Icon Mapping without performance overhead
void UpdateEnemyIconMapping(void* enemyLinker) {
    if (!enemyLinker) {
        return;
    }

    try {
        // Quick check: if mapping already exists, skip
        auto it = enemyPtrToHeroIdMap.find(enemyLinker);
        if (it != enemyPtrToHeroIdMap.end() && !it->second.empty()) {
            return; // Already mapped
        }

        // Simple: Get heroId and create mapping
        std::string heroId = SafeGetHeroId(enemyLinker);

        // Backup: Use spawn order if primary fails
        if (heroId.empty()) {
            auto spawnIt = enemySpawnOrder.find(enemyLinker);
            if (spawnIt != enemySpawnOrder.end()) {
                int spawnOrder = spawnIt->second;
                // Simple fallback: 101, 102, 103, 104, 105
                heroId = std::to_string(101 + (spawnOrder % 5));
            }
        }

        if (!heroId.empty()) {
            // Store mapping: enemyLinker -> heroId
            enemyPtrToHeroIdMap[enemyLinker] = heroId;
            LOGD("FAST MAPPING: Enemy %p -> heroId %s", enemyLinker, heroId.c_str());
        }
    } catch(...) {
        // Ignore errors to maintain performance
    }
}

// FAST & SIMPLE: Get Enemy HeroId with minimal overhead
std::string GetEnemyHeroId(void* enemyLinker) {
    if (!enemyLinker) {
        return "";
    }

    // Quick lookup: if mapping exists, return it
    auto it = enemyPtrToHeroIdMap.find(enemyLinker);
    if (it != enemyPtrToHeroIdMap.end() && !it->second.empty()) {
        return it->second; // Use cached mapping
    }

    // Simple: try to get heroId and cache it
    std::string heroId = SafeGetHeroId(enemyLinker);
    if (!heroId.empty()) {
        enemyPtrToHeroIdMap[enemyLinker] = heroId;
        return heroId;
    }

    return "";
}

// ICON SHUFFLE FIX: Get Or Load Hero Texture with Stable Caching
GLuint GetOrLoadHeroTexture(const std::string& heroId) {
    if (heroId.empty()) {
        return 0;
    }

    // Check cache first
    auto it = heroIdToTextureMap.find(heroId);
    if (it != heroIdToTextureMap.end() && it->second != 0) {
        // Validate texture is still valid
        if (glIsTexture(it->second)) {
            return it->second;
        } else {
            // Remove invalid texture from cache
            heroIdToTextureMap.erase(it);
        }
    }

    // Load texture
    std::string baseImagePath = std::string("/storage/emulated/0/Android/data/") +
                              getPack() +
                              "/files/TH/";
    std::string imagePath = baseImagePath + heroId + ".png";

    if (fileExists(imagePath)) {
        GLuint textureID = LoadTexture(imagePath.c_str());
        if (textureID != 0) {
            heroIdToTextureMap[heroId] = textureID;
            LOGD("ICON SHUFFLE FIX: Loaded texture for heroId %s -> textureId %u", heroId.c_str(), textureID);
            return textureID;
        }
    }

    return 0;
}

// ICON SHUFFLE FIX: Cleanup Enemy Icon Mapping
void CleanupEnemyIconMapping(void* enemyLinker) {
    if (!enemyLinker) {
        return;
    }

    try {
        // Remove from stable mappings
        auto heroIt = enemyPtrToHeroIdMap.find(enemyLinker);
        if (heroIt != enemyPtrToHeroIdMap.end()) {
            LOGD("ICON SHUFFLE FIX: Cleaning up mapping for enemy %p, heroId %s",
                 enemyLinker, heroIt->second.c_str());
            enemyPtrToHeroIdMap.erase(heroIt);
        }

        auto textureIt = enemyPtrToTextureMap.find(enemyLinker);
        if (textureIt != enemyPtrToTextureMap.end()) {
            enemyPtrToTextureMap.erase(textureIt);
        }

        // Also try to clean up objId mapping
        try {
            int objId = ActorLinker_get_ObjID(enemyLinker);
            auto objIt = objIdToHeroIdMap.find(objId);
            if (objIt != objIdToHeroIdMap.end()) {
                objIdToHeroIdMap.erase(objIt);
            }
        } catch(...) {
            // Ignore objId errors
        }
    } catch(...) {
        LOGD("Exception in CleanupEnemyIconMapping for enemy %p", enemyLinker);
    }
}

// ICON SHUFFLE FIX: Get Enemy Texture with Stable Mapping
GLuint GetEnemyTexture(void* enemyLinker) {
    if (!enemyLinker) {
        return 0;
    }

    // ICON SHUFFLE FIX: Try stable texture mapping first
    auto it = enemyPtrToTextureMap.find(enemyLinker);
    if (it != enemyPtrToTextureMap.end() && it->second != 0) {
        // Validate texture is still valid
        if (glIsTexture(it->second)) {
            return it->second;
        } else {
            // Remove invalid texture
            enemyPtrToTextureMap.erase(it);
        }
    }

    // ICON SHUFFLE FIX: Get heroId using stable mapping and load texture
    std::string heroId = GetEnemyHeroId(enemyLinker);
    if (!heroId.empty()) {
        GLuint textureID = GetOrLoadHeroTexture(heroId);
        if (textureID != 0) {
            // Store stable mapping: enemyLinker -> textureID
            enemyPtrToTextureMap[enemyLinker] = textureID;
            LOGD("ICON SHUFFLE FIX: Cached texture for enemy %p, heroId %s -> textureId %u",
                 enemyLinker, heroId.c_str(), textureID);
            return textureID;
        }
    }

    return 0;
}

// FAST & SIMPLE: Lightweight mapping cleanup (only when needed)
void ValidateAndSyncMappings() {
    static float lastValidation = 0;
    float now = ImGui::GetTime();

    // Validate every 5 seconds to minimize performance impact
    if (now - lastValidation < 5.0f) {
        return;
    }
    lastValidation = now;

    try {
        // Quick cleanup: remove mappings for enemies not in cache
        std::vector<void*> validEnemies;
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            for (const auto& enemy : enemyCache) {
                if (enemy.enemyLinker) {
                    validEnemies.push_back(enemy.enemyLinker);
                }
            }
        }

        // Simple cleanup: remove stale mappings
        for (auto it = enemyPtrToHeroIdMap.begin(); it != enemyPtrToHeroIdMap.end();) {
            if (std::find(validEnemies.begin(), validEnemies.end(), it->first) == validEnemies.end()) {
                it = enemyPtrToHeroIdMap.erase(it);
            } else {
                ++it;
            }
        }

    } catch(...) {
        // Ignore errors to maintain performance
    }
}

// Hàm cập nhật cache kẻ địch
void UpdateEnemyCache() {
    // Kiểm tra cơ bản xem ESP đã được khởi tạo và trận đấu đã bắt đầu chưa
    if (!espInitialized || !matchStarted) {
        return;
    }
    
    float cacheTime = ImGui::GetTime();

    // Giới hạn tần suất cập nhật cache
    if (cacheTime - lastCacheUpdate < CACHE_UPDATE_INTERVAL) {
        return;
    }
    lastCacheUpdate = cacheTime;
    
    try {
        // Kiểm tra điều kiện cơ bản
        if (!Config.ESPMenu.Enable_ESP || 
            !espManager || 
            !espManager->enemies || 
            espManager->enemies->empty()) {
            std::lock_guard<std::mutex> lock(cacheMutex);
            enemyCache.clear();
            return;
        }
        
        // Chuẩn bị dữ liệu mới
        std::vector<EnemyData> newCache;
        
        // Thu thập thông tin kẻ địch
        for (int i = 0; i < espManager->enemies->size(); i++) {
            if (i >= espManager->enemies->size()) {
                break; // Phòng trường hợp size thay đổi giữa vòng lặp
            }
            
            void* Enemy = (*espManager->enemies)[i]->object;
            void* EnemyLinker = nullptr;
            
            // Kiểm tra Enemy có tồn tại không
            if (!Enemy) continue;
            
            // Tìm EnemyLinker tương ứng
            if (ActorLinker_enemy && 
                ActorLinker_enemy->enemies && 
                i < ActorLinker_enemy->enemies->size()) {
                EnemyLinker = (*ActorLinker_enemy->enemies)[i]->object;
            }
            
            if (!EnemyLinker) continue;
            
            // Lấy thông tin từ LObjWrapper
            void* LObjWrapper = *(void**)((uint64_t)Enemy + 
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll", "NucleusDrive.Logic", "LActorRoot", "ActorControl"));
                
            if (!LObjWrapper) continue;
            
            // Lấy thông tin sức khỏe
            void* ValuePropertyComponent = *(void**)((uint64_t)Enemy + 
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll", "NucleusDrive.Logic", "LActorRoot", "ValueComponent"));
                
            if (!ValuePropertyComponent) continue;
            
            // Tạo cache dữ liệu mới
            EnemyData enemyData;
            enemyData.enemyObj = Enemy;
            enemyData.enemyLinker = EnemyLinker;
            
            try {
                // Các hàm này có thể gây lỗi nếu đối tượng không hợp lệ
                enemyData.position = VInt2Vector(LActorRoot_get_location(Enemy), LActorRoot_get_forward(Enemy));
                enemyData.hp = ValuePropertyComponent_get_actorHp(ValuePropertyComponent);
                enemyData.maxHp = ValuePropertyComponent_get_actorHpTotal(ValuePropertyComponent);
                enemyData.isVisible = ActorLinker_get_bVisible(EnemyLinker);
                enemyData.isDead = LObjWrapper_get_IsDeadState(LObjWrapper);
            } catch(...) {
                // Nếu có lỗi khi gọi hàm, bỏ qua kẻ địch này
                continue;
            }
            
            newCache.push_back(enemyData);
        }
        
        // Cập nhật cache với mutex
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            enemyCache = newCache;
        }
    } catch(const std::exception& e) {
        LOGD("Exception in UpdateEnemyCache: %s", e.what());
    } catch(...) {
        LOGD("Unknown exception in UpdateEnemyCache");
    }
}

void UpdateEnemyIcons() {
    // Kiểm tra cơ bản xem ESP đã được khởi tạo và trận đấu đã bắt đầu chưa
    if (!espInitialized || !matchStarted) {
        return;
    }

    static float lastIconUpdate = 0;
    static bool lastIconState = false;
    float iconTime = ImGui::GetTime();

    // ICON SHUFFLE FIX: Force update when icon state changes
    bool iconStateChanged = (Config.ESPMenu.MinimapIcon != lastIconState);
    if (iconStateChanged) {
        LOGD("ICON SHUFFLE FIX: Icon state changed, forcing rebuild");
        hasInitializedIcons = false;
        forceUpdate = true;
        lastIconState = Config.ESPMenu.MinimapIcon;
    }

    if(iconTime - lastIconUpdate < ICON_UPDATE_INTERVAL && !forceUpdate) {
        return;
    }

    lastIconUpdate = iconTime;
    
    try {
        // Kiểm tra điều kiện cơ bản
        if (!Config.ESPMenu.Enable_ESP || 
            !espManager || 
            !espManager->enemies || 
            espManager->enemies->empty()) {
            hasInitializedIcons = false;
            enemyIconTextures.clear();
            enemyNames.clear();
            return;
        }
        
        // Kiểm tra ActorLinker_enemy
        if (!ActorLinker_enemy || !ActorLinker_enemy->enemies) {
            return;
        }
        
        if(!hasInitializedIcons || forceUpdate) {
            enemyIconTextures.clear();
            enemyNames.clear();

            LOGD("ICON SHUFFLE FIX: Initializing enemy icons with enhanced stable mapping system");

            // ICON SHUFFLE FIX: Use consistent data source (enemyCache)
            std::vector<EnemyData> currentEnemies;
            {
                std::lock_guard<std::mutex> lock(cacheMutex);
                currentEnemies = enemyCache;
            }

            if (currentEnemies.empty()) {
                LOGD("ICON SHUFFLE FIX: No enemies in cache, skipping icon initialization");
                return;
            }

            // ICON SHUFFLE FIX: Clear all mappings for fresh start
            if (forceUpdate) {
                LOGD("ICON SHUFFLE FIX: Force update - clearing all mappings");
                enemyPtrToHeroIdMap.clear();
                enemyPtrToTextureMap.clear();
            }

            // ICON SHUFFLE FIX: Create stable mappings for ALL current enemies from cache
            for (const auto& enemy : currentEnemies) {
                if (enemy.enemyLinker) {
                    UpdateEnemyIconMapping(enemy.enemyLinker);
                }
            }

            // ICON SHUFFLE FIX: Build icon arrays using consistent data source (enemyCache)
            for (const auto& enemy : currentEnemies) {
                if (!enemy.enemyLinker) continue;

                try {
                    GLuint textureID = GetEnemyTexture(enemy.enemyLinker);
                    if (textureID != 0) {
                        enemyIconTextures.push_back(textureID);

                        // Get hero name
                        std::string heroId = GetEnemyHeroId(enemy.enemyLinker);
                        if (!heroId.empty()) {
                            try {
                                enemyNames.push_back(GetHeroName(stoi(heroId)));
                            } catch(...) {
                                enemyNames.push_back(nullptr);
                            }
                        } else {
                            enemyNames.push_back(nullptr);
                        }
                    }
                } catch(...) {
                    // Skip this enemy on error
                    LOGD("ICON SHUFFLE FIX: Error processing enemy %p, skipping", enemy.enemyLinker);
                    continue;
                }
            }

            hasInitializedIcons = true;
            forceUpdate = false;

            LOGD("ICON SHUFFLE FIX: Icon initialization complete. Loaded %d icons from %d enemies with enhanced stable mapping",
                 (int)enemyIconTextures.size(),
                 (int)currentEnemies.size());
        }
    } catch(const std::exception& e) {
        LOGD("Exception in UpdateEnemyIcons: %s", e.what());
    } catch(...) {
        LOGD("Unknown exception in UpdateEnemyIcons");
    }
}

void DrawCircleHealth(ImVec2 position, int health, int max_health, float radius, float thickness = 4.0f) {
    if (health <= 0 || max_health <= 0) {
        return; // Phòng trường hợp giá trị không hợp lệ
    }
    
    try {
        float healthPercent = (float)health / max_health * 100;
        ImU32 healthColor;
        
        if (healthPercent > 80) healthColor = IM_COL32(210, 0, 0, 255);
        else if (healthPercent > 50) healthColor = IM_COL32(153, 0, 0, 255);
        else if (healthPercent > 30) healthColor = IM_COL32(102, 0, 0, 255);
        else healthColor = IM_COL32(51, 0, 0, 255);
        
        float healthRatio = (float)health / max_health;
        float a_max = 6.28318530718f; // 2*PI pre-calculated
        float endAngle = (-(a_max / 4.0f)) + (a_max * healthRatio);
        
        ImGui::GetForegroundDrawList()->PathArcTo(position, radius, (-(a_max / 2.0f)), endAngle);
        ImGui::GetForegroundDrawList()->PathStroke(healthColor, ImDrawFlags_None, thickness);
    } catch(...) {
        // Bỏ qua lỗi khi vẽ
    }
}

void Draw2DBox(ImDrawList *draw, void *instance, Vector3 EnemyPos, Camera *camera, int glHeight, int glWidth) {
    if (!draw || !instance || !camera) {
        return;
    }
    
    try {
        const float ASPECT_RATIO = 0.6f;
        const float HEALTH_BAR_HEIGHT = 20.0f;
        const float HEALTH_BAR_PADDING = 20.0f;
        const float CORNER_THICKNESS = 5.0f;
        const float BOX_LINE_THICKNESS = 3.0f;
        const float CORNER_LENGTH_RATIO = 0.1f;
        
        Vector3 head = EnemyPos + Vector3(0, 2.2f, 0);
        Vector3 foot = EnemyPos - Vector3(0, 0.2f, 0);
        
        Vector3 headScreen = camera->WorldToScreenPoint(head);
        Vector3 footScreen = camera->WorldToScreenPoint(foot);
        
        if (headScreen.z > 0 && footScreen.z > 0) {
            ImVec2 headPos = ImVec2(headScreen.x, glHeight - headScreen.y);
            ImVec2 footPos = ImVec2(footScreen.x, glHeight - footScreen.y);
            
            float height = abs(headPos.y - footPos.y);
            float width = height * ASPECT_RATIO;
            float centerX = (headPos.x + footPos.x) / 2;
            
            ImVec2 topLeft = ImVec2(centerX - width/2, headPos.y);
            ImVec2 topRight = ImVec2(centerX + width/2, headPos.y);
            ImVec2 bottomLeft = ImVec2(centerX - width/2, footPos.y);
            ImVec2 bottomRight = ImVec2(centerX + width/2, footPos.y);

            // Draw box frame
            draw->AddRect(topLeft, bottomRight, IM_COL32(255, 255, 255, 255), 0.0f, ImDrawFlags_None, BOX_LINE_THICKNESS);
            
            float cornerLength = height * CORNER_LENGTH_RATIO;
            ImU32 cornerColor = IM_COL32(255, 0, 0, 255);
            
            // Draw corners
            // Top left
            draw->AddLine(topLeft, ImVec2(topLeft.x + cornerLength, topLeft.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(topLeft, ImVec2(topLeft.x, topLeft.y + cornerLength), cornerColor, CORNER_THICKNESS);
            
            // Top right
            draw->AddLine(topRight, ImVec2(topRight.x - cornerLength, topRight.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(topRight, ImVec2(topRight.x, topRight.y + cornerLength), cornerColor, CORNER_THICKNESS);
            
            // Bottom left
            draw->AddLine(bottomLeft, ImVec2(bottomLeft.x + cornerLength, bottomLeft.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(bottomLeft, ImVec2(bottomLeft.x, bottomLeft.y - cornerLength), cornerColor, CORNER_THICKNESS);
            
            // Bottom right
            draw->AddLine(bottomRight, ImVec2(bottomRight.x - cornerLength, bottomRight.y), cornerColor, CORNER_THICKNESS);
            draw->AddLine(bottomRight, ImVec2(bottomRight.x, bottomRight.y - cornerLength), cornerColor, CORNER_THICKNESS);

            // Get HP info
            void *ValuePropertyComponent = *(void**)((uint64_t)instance + 
                IL2Cpp::Il2CppGetFieldOffset("Project.Plugins_d.dll","NucleusDrive.Logic","LActorRoot","ValueComponent"));
                
            if (!ValuePropertyComponent) return;
            
            int currentHp = 0;
            int maxHp = 1;
            
            try {
                currentHp = ValuePropertyComponent_get_actorHp(ValuePropertyComponent);
                maxHp = ValuePropertyComponent_get_actorHpTotal(ValuePropertyComponent);
                
                // Phòng trường hợp maxHp = 0 sẽ gây lỗi chia 0
                if (maxHp <= 0) maxHp = 1;
            } catch(...) {
                // Nếu có lỗi khi lấy thông tin máu, sử dụng giá trị mặc định
                currentHp = 0;
                maxHp = 1;
            }
            
            float hpPercentage = (float)currentHp / maxHp;
            
            float healthBarWidth = width * 1.2f;
            ImVec2 healthBarPos = ImVec2(centerX - healthBarWidth/2, bottomRight.y + HEALTH_BAR_PADDING);
            
            ImVec2 healthBarEnd = ImVec2(healthBarPos.x + healthBarWidth, healthBarPos.y + HEALTH_BAR_HEIGHT);
            
            // Draw health bar background
            draw->AddRectFilled(healthBarPos, healthBarEnd, IM_COL32(51, 51, 51, 255), 10.0f);
            
            ImVec2 currentHealthEnd = ImVec2(healthBarPos.x + (healthBarWidth * hpPercentage), healthBarEnd.y);
            
            // Determine health color based on percentage
            ImU32 healthColor;
            if (hpPercentage > 0.9f) healthColor = IM_COL32(0, 255, 0, 255);
            else if (hpPercentage > 0.8f) healthColor = IM_COL32(128, 255, 0, 255);
            else if (hpPercentage > 0.7f) healthColor = IM_COL32(255, 255, 0, 255);
            else if (hpPercentage > 0.6f) healthColor = IM_COL32(255, 215, 0, 255);
            else if (hpPercentage > 0.5f) healthColor = IM_COL32(255, 165, 0, 255);
            else if (hpPercentage > 0.4f) healthColor = IM_COL32(255, 140, 0, 255);
            else if (hpPercentage > 0.3f) healthColor = IM_COL32(255, 69, 0, 255);
            else if (hpPercentage > 0.2f) healthColor = IM_COL32(255, 0, 0, 255);
            else if (hpPercentage > 0.1f) healthColor = IM_COL32(204, 0, 0, 255);
            else healthColor = IM_COL32(139, 0, 0, 255);
            
            // Draw current health
            draw->AddRectFilled(healthBarPos, currentHealthEnd, healthColor, 10.0f);
            
            // Draw health bar border
            draw->AddRect(healthBarPos, healthBarEnd, IM_COL32(255, 255, 255, 255), 10.0f, ImDrawFlags_None, 2.0f);
            
            // Draw health percentage
            char hpText[32];
            sprintf(hpText, "%d%%", (int)(hpPercentage * 100));
            ImVec2 textSize = ImGui::CalcTextSize(hpText);
            draw->AddText(
                ImVec2(centerX - textSize.x/2, healthBarPos.y + (HEALTH_BAR_HEIGHT - textSize.y)/2),
                IM_COL32(255, 255, 255, 255),
                hpText
            );
        }
    } catch(const std::exception& e) {
        LOGD("Exception in Draw2DBox: %s", e.what());
    } catch(...) {
        LOGD("Unknown exception in Draw2DBox");
    }
}

// ==================== AIM LINE DRAWING FUNCTION ====================
void DrawAimLine(ImDrawList *draw) {
    if (!draw || !aimShowTargetLine) {
        return;
    }

    try {
        // Check if we have a valid aim target
        if (AimEnemyTarget.myPos == Vector3::zero() || AimEnemyTarget.enemyPos == Vector3::zero()) {
            return;
        }

        // Get camera for world to screen conversion
        Camera* camera = Camera::get_main();
        if (!camera) {
            return;
        }

        // Convert world positions to screen positions
        Vector3 playerScreenPos = camera->WorldToScreenPoint(AimEnemyTarget.myPos);
        Vector3 targetScreenPos = camera->WorldToScreenPoint(AimEnemyTarget.enemyPos);

        // Check if both positions are in front of camera
        if (playerScreenPos.z <= 0 || targetScreenPos.z <= 0) {
            return;
        }

        // Convert to ImGui screen coordinates (flip Y axis)
        ImVec2 playerPos = ImVec2(playerScreenPos.x, glHeight - playerScreenPos.y);
        ImVec2 targetPos = ImVec2(targetScreenPos.x, glHeight - targetScreenPos.y);

        // Check if positions are within screen bounds
        if (playerPos.x < 0 || playerPos.x > glWidth || playerPos.y < 0 || playerPos.y > glHeight ||
            targetPos.x < 0 || targetPos.x > glWidth || targetPos.y < 0 || targetPos.y > glHeight) {
            return;
        }

        // Calculate distance for dynamic line styling
        float distance = Vector3::Distance(AimEnemyTarget.myPos, AimEnemyTarget.enemyPos);

        // Dynamic color based on distance (closer = more red, farther = more green)
        ImU32 lineColor;
        if (distance < 10.0f) {
            // Close range - Red to Orange
            float ratio = distance / 10.0f;
            lineColor = IM_COL32(255, (int)(ratio * 165), 0, 200); // Red to Orange
        } else if (distance < 25.0f) {
            // Medium range - Orange to Yellow
            float ratio = (distance - 10.0f) / 15.0f;
            lineColor = IM_COL32(255, (int)(165 + ratio * 90), 0, 200); // Orange to Yellow
        } else {
            // Long range - Yellow to Green
            float ratio = std::min((distance - 25.0f) / 25.0f, 1.0f);
            lineColor = IM_COL32((int)(255 - ratio * 255), 255, 0, 200); // Yellow to Green
        }

        // Draw main aim line with dynamic thickness
        float dynamicThickness = aimLineThickness + (distance > 20.0f ? 1.0f : 0.0f);
        draw->AddLine(playerPos, targetPos, lineColor, dynamicThickness);

        // Draw decorative elements
        // Player position indicator (small circle)
        draw->AddCircleFilled(playerPos, 4.0f, IM_COL32(255, 255, 255, 255));
        draw->AddCircle(playerPos, 4.0f, IM_COL32(0, 0, 0, 255), 12, 1.5f);

        // Target position indicator (larger circle with pulsing effect)
        static float pulseTime = 0.0f;
        pulseTime += ImGui::GetIO().DeltaTime * 3.0f; // Pulse speed
        float pulseRadius = 6.0f + sin(pulseTime) * 2.0f; // Pulsing radius

        draw->AddCircleFilled(targetPos, pulseRadius, lineColor);
        draw->AddCircle(targetPos, pulseRadius, IM_COL32(255, 255, 255, 255), 12, 1.5f);

        // Draw distance text near the line center
        ImVec2 lineCenter = ImVec2((playerPos.x + targetPos.x) * 0.5f, (playerPos.y + targetPos.y) * 0.5f);
        char distanceText[32];
        snprintf(distanceText, sizeof(distanceText), "%.1fm", distance);

        // Text background for better readability
        ImVec2 textSize = ImGui::CalcTextSize(distanceText);
        ImVec2 textPos = ImVec2(lineCenter.x - textSize.x * 0.5f, lineCenter.y - textSize.y * 0.5f);

        draw->AddRectFilled(
            ImVec2(textPos.x - 4, textPos.y - 2),
            ImVec2(textPos.x + textSize.x + 4, textPos.y + textSize.y + 2),
            IM_COL32(0, 0, 0, 150),
            3.0f
        );

        draw->AddText(textPos, IM_COL32(255, 255, 255, 255), distanceText);

        // Optional: Draw arrow pointing to target
        Vector3 direction = AimEnemyTarget.enemyPos - AimEnemyTarget.myPos;
        direction.Normalize();

        // Calculate arrow points
        float arrowSize = 8.0f;
        ImVec2 arrowTip = targetPos;
        ImVec2 arrowBase = ImVec2(
            targetPos.x - direction.x * arrowSize * 2,
            targetPos.y + direction.z * arrowSize * 2 // Note: using z for 2D projection
        );

        ImVec2 arrowLeft = ImVec2(
            arrowBase.x - direction.z * arrowSize,
            arrowBase.y - direction.x * arrowSize
        );

        ImVec2 arrowRight = ImVec2(
            arrowBase.x + direction.z * arrowSize,
            arrowBase.y + direction.x * arrowSize
        );

        // Draw arrow
        draw->AddTriangleFilled(arrowTip, arrowLeft, arrowRight, lineColor);
        draw->AddTriangle(arrowTip, arrowLeft, arrowRight, IM_COL32(255, 255, 255, 255), 1.5f);

    } catch (const std::exception& e) {
        LOGD("Exception in DrawAimLine: %s", e.what());
    } catch (...) {
        LOGD("Unknown exception in DrawAimLine");
    }
}

void DrawESP(ImDrawList *draw) {
    // Khởi tạo ESP nếu chưa được khởi tạo
    if (!espInitialized) {
        InitializeESP();
    }

    // Luôn check for new match trước
    CheckForNewMatch();

    // Kiểm tra đơn giản: ESP có được phép chạy không
    if (!IsESPAllowedToRun()) {
        return;
    }

    static float lastEspUpdate = 0;
    float espTime = ImGui::GetTime();

    // Limit ESP update frequency to ~60 FPS
    if(espTime - lastEspUpdate < ESP_UPDATE_INTERVAL) {
        return;
    }

    lastEspUpdate = espTime;

    // ICON SHUFFLE FIX: Validate mappings before updating
    ValidateAndSyncMappings();

    // Cập nhật dữ liệu
    UpdateEnemyCache();
    UpdateEnemyIcons();
    
    // Kiểm tra espManager và enemies
    if (!espManager || !espManager->enemies || espManager->enemies->empty()) {
        return;
    }

    try {
        // Lấy thông tin người chơi
        void *actorLinker = espManager->MyPlayer;
        if (!actorLinker) {
            // Nếu không có thông tin người chơi, đánh dấu trận đấu chưa bắt đầu
            matchStarted = false;
            return;
        }

        // Kiểm tra camera
        Camera* mainCamera = Camera::get_main();
        if (!mainCamera) {
            return;
        }
        
        Vector3 myPos;
        try {
            myPos = ActorLinker_getPosition((void *)actorLinker);
        } catch(...) {
            LOGD("Exception when getting player position");
            return;
        }
        
        Vector3 myPosSC = mainCamera->WorldToScreenPoint(myPos);
        ImVec2 myPos_Vec2 = ImVec2(glWidth - myPosSC.x, myPosSC.y);

        if (myPosSC.z > 0) {
            myPos_Vec2 = ImVec2(myPosSC.x, glHeight - myPosSC.y);
        }

        // Update map scale based on screen size
        if(scaleX == 1) {
            mapNgh = 3.325f;
            radiusMini = 18;
        } else if(scaleX >= 0.6f) {
            mapNgh = 2.255f;
            radiusMini = 12;
        } else {
            mapNgh = 1.835f;
            radiusMini = 9;
        }

        // Minimap handling
        MinimapSys *minimapSys = MinimapSys::get_TheMinimapSys();
        MinimapSys_CMapTransferData *mMapTransferData = NULL;
        
        if(minimapSys != NULL)
            mMapTransferData = minimapSys->mMapTransferData();

        std::string baseImagePath = std::string("/storage/emulated/0/Android/data/") + 
                                  getPack() + 
                                  "/files/TH/";

        // Duyệt qua các kẻ địch trong cache
        std::vector<EnemyData> currentEnemies;
        {
            std::lock_guard<std::mutex> lock(cacheMutex);
            currentEnemies = enemyCache;
        }
        
        // Nếu không có kẻ địch trong cache, thoát
        if (currentEnemies.empty()) {
            return;
        }

        for (const auto& enemy : currentEnemies) {
            // Bỏ qua kẻ địch đã chết
            if (enemy.isDead || enemy.hp <= 0) {
                continue;
            }

            // Kiểm tra cài đặt bỏ qua kẻ địch tàng hình
            if (Config.ESPMenu.IgnoreInvisible && !enemy.isVisible) {
                continue;
            }

            void* Enemy = enemy.enemyObj;
            void* EnemyLinker = enemy.enemyLinker;
            Vector3 EnemyPos = enemy.position;

            // Draw minimap icon
            if(Config.ESPMenu.MinimapIcon && mMapTransferData != NULL && minimapSys != NULL) {
                // Use stable mapping system
                std::string heroId = GetEnemyHeroId(EnemyLinker);

                if (!heroId.empty()) {
                    std::string imagePath = baseImagePath + heroId + ".png";
                    
                    // Tính toán base scale dựa trên kích thước màn hình
                    float baseScale = scaleX == 1 ? 3.325f : 
                                    scaleX >= 0.6f ? 2.255f : 1.835f;
                    
                    // Tính mapNgh dựa trên loại map
                    if (minimapSys->mapType() == 1) {
                        // Mini map
                        mapNgh = baseScale * (Config.ESPMenu.MapSize / 240.0f);
                    } else {
                        // Big map
                        mapNgh = baseScale * (Config.ESPMenu.BigMapSize / 240.0f);
                    }
                    
                    // Đảo ngược mapNgh cho team địch
                    if(ActorLinker_COM_PLAYERCAMP(EnemyLinker) == 1) {
                        mapNgh *= -1;
                    }
                    
                    // Tính toán vị trí hiển thị trên map
                    ImVec2 minimapPos;
                    float iconScale = minimapSys->mapType() == 1 ? 1.0f : 2.0f; // Scale up for big map
                    
                    if(minimapSys->mapType() == 1) {
                        minimapPos = ImVec2(
                            mMapTransferData->mmFinalScreenPos().x + Config.ESPMenu.MapOffsetX + (EnemyPos.x * mapNgh),
                            glHeight - mMapTransferData->mmFinalScreenPos().y + Config.ESPMenu.MapOffsetY - (EnemyPos.z * mapNgh)
                        );
                    } else {
                        // Big map - Logic phức tạp hơn để xử lý đúng cả 2 trường hợp
                        int myPlayerCamp = 0;
                        if (espManager->MyPlayer != NULL) {
                            myPlayerCamp = ActorLinker_COM_PLAYERCAMP(espManager->MyPlayer);
                        }
                        int enemyCamp = ActorLinker_COM_PLAYERCAMP(EnemyLinker);

                        // Logic: Quay 180 độ khi player là team xanh VÀ enemy là team đỏ
                        float rotationMultiplier = 1.0f;
                        if (myPlayerCamp == 0 && enemyCamp == 1) {
                            rotationMultiplier = -1.0f; // Quay 180 độ
                        }

                        minimapPos = ImVec2(
                            mMapTransferData->bmFinalScreenPos().x + (EnemyPos.x * mapNgh * mMapTransferData->bmScale().x) * rotationMultiplier,
                            glHeight - mMapTransferData->bmFinalScreenPos().y - (EnemyPos.z * mapNgh * mMapTransferData->bmScale().y) * rotationMultiplier
                        );
                    }
                    
                    // Use stable texture system
                    GLuint textureID = GetEnemyTexture(EnemyLinker);
                    if(textureID) {
                            float baseIconSize = Config.ESPMenu.IconSize;
                            float iconRadius = baseIconSize * iconScale;
                            float healthRadius = iconRadius + (2.0f * iconScale);
                            float iconSize = iconRadius * 1.8f;
                            
                            // Vẽ background
                            draw->AddCircleFilled(minimapPos, iconRadius, IM_COL32(0, 0, 0, 180));
                            
                            // Vẽ hero icon
                            ImVec2 iconMin(minimapPos.x - iconSize * 0.5f, minimapPos.y - iconSize * 0.5f);
                            ImVec2 iconMax(minimapPos.x + iconSize * 0.5f, minimapPos.y + iconSize * 0.5f);
                            
                            draw->AddImage(
                                (void*)textureID, 
                                iconMin,
                                iconMax,
                                ImVec2(0.0f, 1.0f),
                                ImVec2(1.0f, 0.0f)
                            );
                            
                            // Lấy thông tin máu từ cache
                            float healthRatio = (float)enemy.hp / enemy.maxHp;
                            
                            // Vẽ vòng máu background
                            const float PI = 3.14159265359f;
                            draw->PathArcTo(
                                minimapPos,
                                healthRadius,
                                -PI,
                                PI,
                                32
                            );
                            draw->PathStroke(IM_COL32(139, 69, 19, 120), ImDrawFlags_None, 3.0f * iconScale);
                            
                            // Vẽ vòng máu hiện tại
                            if(healthRatio > 0) {
                                float startAngle = PI * 0.5f - (PI * healthRatio);
                                float endAngle = PI * 0.5f + (PI * healthRatio);
                                
                                ImU32 healthColor = healthRatio > 0.7f ? IM_COL32(200, 0, 0, 255) :
                                                   healthRatio > 0.5f ? IM_COL32(190, 0, 0, 255) :
                                                   healthRatio > 0.3f ? IM_COL32(180, 0, 0, 255) :
                                                                      IM_COL32(170, 0, 0, 255);
                                
                                draw->PathArcTo(minimapPos, healthRadius, startAngle, endAngle, 32);
                                draw->PathStroke(healthColor, ImDrawFlags_None, 3.0f * iconScale);
                            }
                        }
                    }
                }
            

            // Draw invisible enemy icons
            if(Config.ESPMenu.Icon && !enemy.isVisible) {
                Vector3 head = EnemyPos + Vector3(0, 2.2f, 0);
                Vector3 foot = EnemyPos - Vector3(0, 0.2f, 0);
                
                Vector3 headScreen = Camera::get_main()->WorldToScreenPoint(head);
                Vector3 footScreen = Camera::get_main()->WorldToScreenPoint(foot);
                
                if (headScreen.z > 0 && footScreen.z > 0) {
                    ImVec2 headPos = ImVec2(headScreen.x, glHeight - headScreen.y);
                    ImVec2 footPos = ImVec2(footScreen.x, glHeight - footScreen.y);
                    
                    float height = abs(headPos.y - footPos.y);
                    float width = height * 0.6f;
                    float centerX = (headPos.x + footPos.x) / 2;
                    
                    ImVec2 boxCenter = ImVec2(centerX, headPos.y + height/2);

                    // Use stable mapping system
                    GLuint textureID = GetEnemyTexture(EnemyLinker);
                    if(textureID) {
                        ImVec2 uv0 = ImVec2(1.0f, 1.0f);
                        ImVec2 uv1 = ImVec2(0.0f, 0.0f);
                        float iconSize = width;
                        ImVec2 iconTopLeft = ImVec2(boxCenter.x - iconSize/2, boxCenter.y - iconSize/2);
                        ImVec2 iconBottomRight = ImVec2(boxCenter.x + iconSize/2, boxCenter.y + iconSize/2);

                        draw->AddImage((void*)textureID, iconTopLeft, iconBottomRight, uv0, uv1);
                        DrawCircleHealth(boxCenter, enemy.hp, enemy.maxHp, iconSize * 0.52f);
                    }
                }
            }

            // Draw player box
            if (Config.ESPMenu.PlayerBox) {
                Draw2DBox(draw, Enemy, EnemyPos, Camera::get_main(), glHeight, glWidth);
            }

            // Draw player line
            if (Config.ESPMenu.PlayerLine) {
                Vector3 headPos = EnemyPos + Vector3(0, 2.2f, 0);
                Vector3 footPos = EnemyPos - Vector3(0, 0.2f, 0);
                
                Vector3 headScreen = Camera::get_main()->WorldToScreenPoint(headPos);
                Vector3 footScreen = Camera::get_main()->WorldToScreenPoint(footPos);
                
                if (headScreen.z > 0 && footScreen.z > 0) {
                    ImVec2 screenHead(headScreen.x, glHeight - headScreen.y);
                    ImVec2 screenFoot(footScreen.x, glHeight - footScreen.y);
                    
                    float centerX = (screenHead.x + screenFoot.x) / 2;
                    ImVec2 bottomCenter(centerX, screenFoot.y);
                    
                    draw->AddLine(
                        myPos_Vec2,
                        bottomCenter,
                        IM_COL32(255, 0, 0, Config.Color.line[3] * 255.0f),
                        1.7f
                    );
                    draw->AddCircleFilled(myPos_Vec2, 4.0f, IM_COL32(255, 255, 255, 255));
                    draw->AddCircleFilled(bottomCenter, 4.0f, IM_COL32(255, 255, 255, 255));
                }
            }
        }

        // ENHANCED: Draw aim line to current target
        DrawAimLine(draw);

    } catch(const std::exception& e) {
        LOGD("Exception in DrawESP: %s", e.what());
    } catch(...) {
        LOGD("Unknown exception in DrawESP");
    }
}

// Hook functions cần được sửa đổi để xử lý lỗi
void (*old_ActorLinker_ActorDestroy)(void *instance);
void ActorLinker_ActorDestroy(void *instance) {
    if (!instance) {
        if (old_ActorLinker_ActorDestroy) {
            old_ActorLinker_ActorDestroy(NULL);
        }
        return;
    }
    
    try {
        // Clean up stable mappings
        CleanupEnemyIconMapping(instance);

        old_ActorLinker_ActorDestroy(instance);
        
        if (ActorLinker_enemy) {
            ActorLinker_enemy->removeEnemyGivenObject(instance);
        }
        
        if (espManager && espManager->MyPlayer == instance) {
            espManager->MyPlayer = NULL;
        }
        
        // Cập nhật cache khi có đối tượng bị hủy
        UpdateEnemyCache();
    } catch(...) {
        LOGD("Exception in ActorLinker_ActorDestroy");
        if (old_ActorLinker_ActorDestroy) {
            old_ActorLinker_ActorDestroy(instance);
        }
    }
}

void (*old_LActorRoot_ActorDestroy)(void *instance, bool bTriggerEvent);
void LActorRoot_ActorDestroy(void *instance, bool bTriggerEvent) {
    if (!instance) {
        if (old_LActorRoot_ActorDestroy) {
            old_LActorRoot_ActorDestroy(NULL, bTriggerEvent);
        }
        return;
    }

    try {
        // ICON SHUFFLE FIX: Clean up mappings before destroying
        CleanupEnemyIconMapping(instance);

        old_LActorRoot_ActorDestroy(instance, bTriggerEvent);

        if (espManager) {
            espManager->removeEnemyGivenObject(instance);
        }

        // ICON SHUFFLE FIX: Force icon rebuild after enemy destruction
        hasInitializedIcons = false;
        forceUpdate = true;

        // Cập nhật cache khi có đối tượng bị hủy
        UpdateEnemyCache();
    } catch(...) {
        LOGD("Exception in LActorRoot_ActorDestroy");
        if (old_LActorRoot_ActorDestroy) {
            old_LActorRoot_ActorDestroy(instance, bTriggerEvent);
        }
    }
}

void (*old_ActorLinker_Update)(void *instance);
void ActorLinker_Update(void *instance) {
    if (!instance) {
        if (old_ActorLinker_Update) {
            old_ActorLinker_Update(NULL);
        }
        return;
    }
    
    try {
        old_ActorLinker_Update(instance);
        
        // Kiểm tra espManager có tồn tại không
        if (!espManager || !ActorLinker_enemy) {
            return;
        }
        
        if(espManager->isEnemyPresent(instance)) {
            return;
        }
        
        if(ActorLinker_enemy->isEnemyPresent(instance)) {
            return;
        }
        
        int actorType = -1;
        try {
            actorType = ActorLinker_ActorTypeDef(instance);
        } catch(...) {
            LOGD("Exception when getting ActorTypeDef");
            return;
        }
            
        if (actorType == 0) {
            bool isHostPlayer = false;
            
            try {
                isHostPlayer = ActorLinker_IsHostPlayer(instance);
            } catch(...) {
                LOGD("Exception when checking IsHostPlayer");
                return;
            }
            
            if (isHostPlayer) {
                espManager->tryAddMyPlayer(instance);
            } else {
                if(espManager->MyPlayer != NULL) {
                    int myPlayerCamp = 0;
                    int instanceCamp = 0;
                    
                    try {
                        myPlayerCamp = ActorLinker_COM_PLAYERCAMP(espManager->MyPlayer);
                        instanceCamp = ActorLinker_COM_PLAYERCAMP(instance);
                    } catch(...) {
                        LOGD("Exception when getting player camp");
                        return;
                    }
                    
                    if(myPlayerCamp != instanceCamp) {
                        ActorLinker_enemy->tryAddEnemy(instance);

                        // CRITICAL FIX: Record spawn order for backup mapping
                        enemySpawnOrder[instance] = globalSpawnCounter++;
                        LOGD("CRITICAL FIX: Enemy %p spawned with order %d", instance, enemySpawnOrder[instance]);

                        // FAST & SIMPLE: Immediate mapping without delays
                        UpdateEnemyIconMapping(instance);
                        forceUpdate = true;

                        // Cập nhật cache khi có thay đổi kẻ địch
                        UpdateEnemyCache();
                    }
                }
            }
        }
    } catch(const std::exception& e) {
        LOGD("Exception in ActorLinker_Update: %s", e.what());
        if (old_ActorLinker_Update) {
            old_ActorLinker_Update(instance);
        }
    } catch(...) {
        LOGD("Unknown exception in ActorLinker_Update");
        if (old_ActorLinker_Update) {
            old_ActorLinker_Update(instance);
        }
    }
}

void (*old_LActorRoot_UpdateLogic)(void *instance, int delta);
void LActorRoot_UpdateLogic(void *instance, int delta) {
    if (!instance) {
        if (old_LActorRoot_UpdateLogic) {
            old_LActorRoot_UpdateLogic(NULL, delta);
        }
        return;
    }
    
    try {
        old_LActorRoot_UpdateLogic(instance, delta);
        
        if (!espManager) {
            return;
        }
        
        if(espManager->isEnemyPresent(instance)) {
            return;
        }
            
        if (espManager->MyPlayer != NULL) {
            void* heroWrapper = NULL;
            int instanceCamp = -1;
            int myPlayerCamp = -1;
            
            try {
                heroWrapper = (void*)LActorRoot_LHeroWrapper(instance);
                if (!heroWrapper) {
                    return;
                }
                
                instanceCamp = LActorRoot_COM_PLAYERCAMP(instance);
                myPlayerCamp = ActorLinker_COM_PLAYERCAMP(espManager->MyPlayer);
            } catch(...) {
                LOGD("Exception when checking hero wrapper");
                return;
            }
            
            if (instanceCamp == myPlayerCamp) {
                espManager->tryAddEnemy(instance);
                
                // Cập nhật cache khi có thay đổi kẻ địch
                UpdateEnemyCache();
            }
        }
    } catch(const std::exception& e) {
        LOGD("Exception in LActorRoot_UpdateLogic: %s", e.what());
        if (old_LActorRoot_UpdateLogic) {
            old_LActorRoot_UpdateLogic(instance, delta);
        }
    } catch(...) {
        LOGD("Unknown exception in LActorRoot_UpdateLogic");
        if (old_LActorRoot_UpdateLogic) {
            old_LActorRoot_UpdateLogic(instance, delta);
        }
    }
}











