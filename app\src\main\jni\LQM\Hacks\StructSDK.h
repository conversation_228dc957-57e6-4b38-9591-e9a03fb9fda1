#pragma once


 //64
class MinimapSys_CMapTransferData {
    public:
    
    Vector2 mmFinalScreenSize() {
        return *(Vector2 *) ((uintptr_t) this + 0x10); //public Vector2 mmFinalScreenSize; // 0x10
    }
    
    Vector2 mmFinalScreenPos() {
        return *(Vector2 *) ((uintptr_t) this + 0x18); //public Vector2 mmFinalScreenPos; // 0x18
    }
    
    Vector2 mmTextureSize() {
        return *(Vector2 *) ((uintptr_t) this + 0x20); //   public Vector2 mmTextureSize; // 0x20
    }
    
    
    Vector3 mmScale() {
        return *(Vector3 *) ((uintptr_t) this + 0x28); //public Vector3 mmScale; // 0x28
    }
    
    
    Vector2 mmUGUISizeData() {
        return *(Vector2 *) ((uintptr_t) this + 0x34); //public Vector2 mmUGUISizeData; // 0x34
    }
    
    Vector2 mmUGUIAnchorPos() {
        return *(Vector2 *) ((uintptr_t) this + 0x3C); //public Vector2 mmUGUIAnchorPos; // 0x3C
    }
    
    float mmRotation() {
        return *(float *) ((uintptr_t) this + 0x44);//public float mmRotation; // 0x44
    }
    
    Vector3 mmSpt3DTranPos() {
        return *(Vector3 *) ((uintptr_t) this + 0x54); //public Vector3 mmSpt3DTranPos; // 0x54
    }
    
    Vector2 bmFinalScreenPos() {
        return *(Vector2 *) ((uintptr_t) this + 0x60); //public Vector2 bmFinalScreenPos; // 0x60
    }
    
    Vector2 bmFinalScreenSize() {
        return *(Vector2 *) ((uintptr_t) this + 0x68); // public Vector2 bmFinalScreenSize; // 0x68
    }
    
    Vector2 bmTextureSize() {
        return *(Vector2 *) ((uintptr_t) this + 0x70); // public Vector2 bmTextureSize; // 0x70
    }
    
    Vector2 bmUGUISizeData() {
        return *(Vector2 *) ((uintptr_t) this + 0x78); // public Vector2 bmUGUISizeData; // 0x78
    }
    
    Vector2 bmUGUIInitAnchorPos() {
        return *(Vector2 *) ((uintptr_t) this + 0x80);//public Vector2 bmUGUIInitAnchorPos; // 0x80
    }
    
    Vector3 bmScale() {
        return *(Vector3 *) ((uintptr_t) this + 0x88); //public Vector3 bmScale; // 0x88
    }
    
    float bmRotation() {
        return *(float *) ((uintptr_t) this + 0x94); // public float bmRotation; // 0x94
    }
    
    Vector2 bmScreenOffsetVec() {
        return *(Vector2 *) ((uintptr_t)this + 0x98); // public Vector2 bmScreenOffsetVec; // 0x98
    }
    Vector3 bmSpt3DTranPos() {
        return *(Vector3 *) ((uintptr_t) this + 0xA4); //public Vector3 bmSpt3DTranPos; // 0xA4
    }
    
};


class MinimapSys {
    
    public:
    
    static MinimapSys *get_TheMinimapSys() {
        MinimapSys *(*get_TheMinimapSys_) () = (MinimapSys *(*)()) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("BattleFormHub"), OBFUSCATE("get_TheMinimapSys"), 0));
        
        return get_TheMinimapSys_();
    }
    
    MinimapSys_CMapTransferData *mMapTransferData() {
        
        return *(MinimapSys_CMapTransferData **) ((uintptr_t) this + 0x30); //private MinimapSys.CMapTransferData mMapTransferData; // 0x20
        
    }
    
    
    int mapType() {
   
      return *(int *) ((uintptr_t) this + 0x50); //private MinimapSys.EMapType curMapType; // 0x38
    }
    
    
};

class Camera {
    public:
    
    static Camera *get_main() {
        Camera *(*get_main_) () = (Camera *(*)()) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera"), OBFUSCATE("get_main"), 0));
        
        //Camera *(*get_main_) () = (Camera *(*)()) (il2cppMap + 0x31b8dd0);
        return get_main_();
    }
    
    Vector3 WorldToScreenPoint(Vector3 position) {
        Vector3 (*WorldToScreenPoint_)(Camera *camera, Vector3 position) = (Vector3 (*)(Camera *, Vector3)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera"), OBFUSCATE("WorldToScreenPoint"), 1));
        
        //Vector3 (*WorldToScreenPoint_)(Camera *camera, Vector3 position) = (Vector3 (*)(Camera *, Vector3)) (il2cppMap + 0x31b84c0);
        return WorldToScreenPoint_(this, position);
    }
};


class CActorInfo {
    public:
    
    String *ActorName() {
        return *(String **) ((uintptr_t) this + IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("CActorInfo"), OBFUSCATE("ActorName")));
        //return *(String **) ((uintptr_t) this + 0xC);
    }
    
};


