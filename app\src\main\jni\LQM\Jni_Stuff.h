#pragma once


#include "ImGui/imgui.h"
#include "ImGui/imgui_impl_android.h"
#include "StrEnc.h"

#include <curl/curl.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <sstream>
#include <iomanip>
JavaVM *jvm;

struct MemoryStruct {
	char *memory;
	size_t size;
};

static size_t WriteMemoryCallback(void *contents, size_t size, size_t nmemb, void *userp) {
	size_t realsize = size * nmemb;
	struct MemoryStruct *mem = (struct MemoryStruct *) userp;
	
	mem->memory = (char *) realloc(mem->memory, mem->size + realsize + 1);
	if (mem->memory == NULL) {
		return 0;
	}
	memcpy(&(mem->memory[mem->size]), contents, realsize);
	mem->size += realsize;
	mem->memory[mem->size] = 0;
	return realsize;
}