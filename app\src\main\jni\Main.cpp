#include <list>
#include <vector>
#include <string.h>
#include <pthread.h>
#include <thread>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <dlfcn.h>
#include "Includes/Logger.h"
#include "Includes/obfuscate.h"
#include "Includes/Utils.h"
#include "KittyMemory/MemoryPatch.h"
#include "Menu/Setup.h"
#include "TranHuong/Tools.h"
#include "TranHuong/fake_dlfcn.h"
#include "TranHuong/Il2Cpp.h"
//Target lib here
#define targetLibName OBFUSCATE("libil2cpp.so")
#define MOD_LIBNAME OBFUSCATE("libESPVN.so")
#define GAME_CODE "aovvn"
#define MOD_VERSION "VN-ESP2"
#define PhienBan ("Mod ESP VN")
#include "Includes/Macros.h"
#include <sys/stat.h>

#include "AES/AES.cpp"





#include "ARMPatch.cpp"
#include "StrEnc.h"

#include <curl/curl.h>
#include "json.hpp"

#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/err.h>
#include <openssl/md5.h>
using json = nlohmann::ordered_json;
using namespace std;


#include <fstream>

#include <EGL/egl.h>
#include <GLES3/gl3.h>

#include "LQM/Call_Me.h"

// AimEntityInfo struct is defined in Hooker.h

struct My_Patches { MemoryPatch HackMap;
} hexPatches;

#include "IconManager/IconManager.h"


/////---OFFSETS ONLINE---////

const char *g_key = OBFUSCATE("094412704612345689");
const char *g_iv = OBFUSCATE("0123456789012345");


string EncryptionAES(const string& strSrc)
{
    size_t length = strSrc.length();
    int block_num = length / BLOCK_SIZE + 1;
    char* szDataIn = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataIn, 0x00, block_num * BLOCK_SIZE + 1);
    strcpy(szDataIn, strSrc.c_str());

    int k = length % BLOCK_SIZE;
    int j = length / BLOCK_SIZE;
    int padding = BLOCK_SIZE - k;
    for (int i = 0; i < padding; i++)
    {
        szDataIn[j * BLOCK_SIZE + k + i] = padding;
    }
    szDataIn[block_num * BLOCK_SIZE] = '\0';

    char *szDataOut = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataOut, 0, block_num * BLOCK_SIZE + 1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Encrypt(szDataIn, szDataOut, block_num * BLOCK_SIZE, AES::CBC);
    string str = base64_encode((unsigned char*) szDataOut,
                               block_num * BLOCK_SIZE);
    delete[] szDataIn;
    delete[] szDataOut;
    return str;
}


string DecryptionAES(const string& strSrc)
{
    string strData = base64_decode(strSrc);
    size_t length = strData.length();
    char *szDataIn = new char[length + 1];
    memcpy(szDataIn, strData.c_str(), length+1);
    char *szDataOut = new char[length + 1];
    memcpy(szDataOut, strData.c_str(), length+1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Decrypt(szDataIn, szDataOut, length, AES::CBC);

    if (0x00 < szDataOut[length - 1] <= 0x16)
    {
        int tmp = szDataOut[length - 1];
        for (int i = length - 1; i >= length - tmp; i--)
        {
            if (szDataOut[i] != tmp)
            {
                memset(szDataOut, 0, length);
                cout << "q" << endl;
                break;
            }
            else
                szDataOut[i] = 0;
        }
    }
    string strDest(szDataOut);
    delete[] szDataIn;
    delete[] szDataOut;
    return strDest;
}



std::string offset = "";
void OffsetsOnline() {
    std::string errMsg;

    struct MemoryStruct chunk{};
    chunk.memory = (char *) malloc(1);
    chunk.size = 0;

    CURL *curl;
    CURLcode res;
    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/files/Check3.php")).c_str());
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");    
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        char data[4096];
        sprintf(data, "game=lqm&nhin_thay_chu_nay=%s","1 đàn chó =)) ");
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);

        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        res = curl_easy_perform(curl);
        
        if (res == CURLE_OK) {
            offset = DecryptionAES(base64_decode(std::string(chunk.memory)));
        } else {
            errMsg = curl_easy_strerror(res);
        }
    }
    curl_easy_cleanup(curl);
}





std::string RandomString(const int len);
std::string CalcMD5(std::string s);
std::string CalcSHA256(std::string s);
std::string RandomString(const int len) {
    static const char alphanumerics[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    srand((unsigned) time(0) * getpid());

    std::string tmp;
    tmp.reserve(len);
    for (int i = 0; i < len; ++i) {
        tmp += alphanumerics[rand() % (sizeof(alphanumerics) - 1)];
    }
    return tmp;
}

std::string CalcMD5(std::string s) {
    std::string result;

    unsigned char hash[MD5_DIGEST_LENGTH];
    char tmp[4];

    MD5_CTX md5;
    MD5_Init(&md5);
    MD5_Update(&md5, s.c_str(), s.length());
    MD5_Final(hash, &md5);
    for (unsigned char i : hash) {
        sprintf(tmp, "%02x", i);
        result += tmp;
    }
    return result;
}




/// HACK MAP ---///
bool HackMap = false;

// AIM SYSTEM GLOBAL VARIABLES
AimEntityInfo AimEnemyTarget;
bool AimElsu = false;
bool aimIsCharging = false;
bool aimIgnoreInvisible = true; // NEW: Option to ignore invisible enemies (default: true for safety)
int aimMode = 0, aimType = 1, aimDrawType = 2, aimSkillSlot = 0;

// ENHANCED AIM SYSTEM CONFIGURATION VARIABLES
bool aimUseESPData = true;      // Use ESP data for more accurate targeting
bool aimPredictMovement = true;  // Predict enemy movement
float aimSmoothness = 1.0f;     // Aim smoothness factor (0.1-2.0)
bool aimShowDebugInfo = false;   // Show debug information
bool aimShowTargetLine = true;   // Show line to current aim target
float aimLineThickness = 2.0f;   // Thickness of aim line (1.0-5.0)



EGLBoolean (*orig_eglSwapBuffers)(EGLDisplay dpy, EGLSurface surface);
EGLBoolean _eglSwapBuffers(EGLDisplay dpy, EGLSurface surface) {
    
    eglQuerySurface(dpy, surface, EGL_WIDTH, &glWidth);
    eglQuerySurface(dpy, surface, EGL_HEIGHT, &glHeight);
    
    if (glWidth <= 0 || glHeight <= 0) {
        return eglSwapBuffers(dpy, surface);
    }
    
    if (!Config.InitImGui.initImGui) {
        ImGui::CreateContext();
        
        ImGui_ImplAndroid_Init();
        ImGui_ImplOpenGL3_Init(OBFUSCATE("#version 300 es"));
        
        ImGuiIO* io = &ImGui::GetIO();
        
        io->ConfigWindowsMoveFromTitleBarOnly = true;
        io->IniFilename = NULL;
        
        static const ImWchar icons_ranges[] = {0x0020, 0x00FF, 0x3000, 0x30FF, 0x31F0, 0x31FF, 0xFF00, 0xFFEF, 0x4e00, 0x9FAF, 0};
        ImFontConfig icons_config;

        ImFontConfig CustomFont;
        CustomFont.FontDataOwnedByAtlas = false;

        icons_config.MergeMode = true;
        icons_config.PixelSnapH = true;
        icons_config.OversampleH = 2.5;
        icons_config.OversampleV = 2.5;

        io->Fonts->AddFontFromMemoryTTF((void *)PIRO_data, PIRO_size, 30.0f, NULL, io->Fonts->GetGlyphRangesVietnamese());
        


        io->Fonts->AddFontFromMemoryTTF(const_cast<std::uint8_t*>(Custom), sizeof(Custom), 21.f, &CustomFont);
        io->Fonts->AddFontFromMemoryCompressedTTF(font_awesome_data, font_awesome_size, 19.0f, &icons_config, icons_ranges);
        
        ImFontConfig font_cfg;
        font_cfg.SizePixels = 22.0f;
        io->Fonts->AddFontDefault(&font_cfg);
        
        Config.InitImGui.initImGui = true;
    }
    
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(glWidth, glHeight);
    ImGui::NewFrame();

    DrawESP(ImGui::GetForegroundDrawList());
    
    ImGui::End();
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    
    
    return orig_eglSwapBuffers(dpy, surface);
}
uintptr_t G_IL2CPP;

void *hack_thread(void *) {
    InitializeIcons();
  espManager = new ESPManager();
    ActorLinker_enemy = new ESPManager();
    while (!il2cppMap) {
        il2cppMap = Tools::GetBaseAddress("libil2cpp.so");
        sleep(1);
    }
    IL2Cpp::Il2CppAttach();
    
    int (*_screenHeight)() = (int (*)()) IL2Cpp::Il2CppGetMethodOffset("UnityEngine.CoreModule.dll","UnityEngine", "Screen", "get_systemHeight",0);
        int (*_screenWidth)() = (int (*)()) IL2Cpp::Il2CppGetMethodOffset("UnityEngine.CoreModule.dll","UnityEngine", "Screen", "get_systemWidth", 0);
    
    if(_screenHeight && _screenWidth){
            screenHeight = _screenHeight();
        
            screenWidth = _screenWidth();
        }
 
      
        
     OffsetsOnline();
    
    std::istringstream iss(offset); 
    std::string line;
    while (std::getline(iss, line)) {
        size_t pos1 = line.find(":");
        size_t pos2 = line.find(":", pos1 + 1);
        size_t pos3 = line.find(":", pos2 + 1);
        size_t pos4 = line.find(":", pos3 + 1);
        size_t pos5 = line.find(":", pos4 + 1);
        size_t pos6 = line.find(":", pos5 + 1);
        size_t pos7 = line.find(":", pos6 + 1);

        std::string str1 = line.substr(0, pos1);
        std::string str2 = line.substr(pos1 + 1, pos2 - pos1 - 1);
        std::string str3 = line.substr(pos2 + 1, pos3 - pos2 - 1);
        std::string str4 = line.substr(pos3 + 1, pos4 - pos3 - 1);
        std::string str5 = line.substr(pos4 + 1, pos5 - pos4 - 1);
        std::string str6 = line.substr(pos5 + 1, pos6 - pos5 - 1);
        std::string str7 = line.substr(pos6 + 1, pos7 - pos6 - 1);
        std::string str8 = line.substr(pos7 + 1);
  
        if (str1 == "PatchIl2cpp") {
            MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(str2.c_str(), str3.c_str(), str4.c_str(), str5.c_str(), std::stoi(str6)), str7.c_str()).Modify();
        } else if (str1 == "PatchAnogs") {
            MemoryPatch::createWithHex("libanogs.so" , string2Offset(str2.c_str()), str3.c_str()).Modify();
        }
    }
           
        

 //   MemoryPatch::createWithHex("libanogs.so", string2Offset(OBFUSCATE("0x1340DC")),  OBFUSCATE("C0035FD6")).Modify();        
 //  MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem") , OBFUSCATE("_LoginSuccess"), 1),"****************"); 
          
                
     Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getWidth")), (void *) _ANativeWindow_getWidth, (void **) &orig_ANativeWindow_getWidth);
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getHeight")), (void *) _ANativeWindow_getHeight, (void **) &orig_ANativeWindow_getHeight);    
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libEGL.so"), OBFUSCATE("eglSwapBuffers")), (void *) _eglSwapBuffers, (void **) &orig_eglSwapBuffers);
   
    AsHero = (uintptr_t(*)(...)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("AsHero"), 0);
    
    GetHero_Icon = (String* (*)(void *, bool))IL2Cpp::Il2CppGetMethodOffset("Project_d.dll","Assets.Scripts.GameSystem","KillNotifyUT","GetHero_Icon",2);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("LateUpdate"), 0), (void *) ActorLinker_Update, (void **) &old_ActorLinker_Update);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("UpdateLogic"), 1), (void *) LActorRoot_UpdateLogic, (void **) &old_LActorRoot_UpdateLogic);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("DestroyActor"), 0), (void *) ActorLinker_ActorDestroy, (void **) &old_ActorLinker_ActorDestroy);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("DestroyActor"), 1), (void *) LActorRoot_ActorDestroy, (void **) &old_LActorRoot_ActorDestroy);
    
    
    //hpPatch = MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("SetHpAndEpToInitialValue"), 3),"E08360B2C0035FD6");
 


    
    ActorLinker_IsHostPlayer = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("IsHostPlayer"), 0);
    ActorLinker_IsHostCamp = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("IsHostCamp"), 0);
    ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objType"), 0);
    ActorLinker_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objCamp"), 0);
    ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_position"), 0);
    ActorLinker_get_HPBarVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_HPBarVisible"), 0);
    ActorLinker_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_ObjID"), 0);
    ActorLinker_get_bVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_bVisible"), 0);

    LActorRoot_get_forward = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_forward"), 0);
    LActorRoot_get_location = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_location"), 0);
    LActorRoot_LHeroWrapper = (uintptr_t (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("AsHero"), 0);
    LActorRoot_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("GiveMyEnemyCamp"), 0);
    LActorRoot_get_bActive = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_bActive"), 0);
    LActorRoot_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_ObjID"), 0);

    GetHeroName = (String *(*)(int)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CHeroInfo") , OBFUSCATE("GetHeroName"), 1);
    

    LObjWrapper_get_IsDeadState = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LObjWrapper") , OBFUSCATE("get_IsDeadState"), 0);

    ValuePropertyComponent_get_actorHp = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHp"), 0);
    ValuePropertyComponent_get_actorHpTotal = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHpTotal"), 0);
    
    
    
    OnCameraHeightChanged = (void(*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("OnCameraHeightChanged"), 0);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("Update"), 0), (void *) CameraSystemUpdate, (void **) &old_CameraSystemUpdate);

    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("GetCameraHeightRateValue"), 1), (void *) GetCameraHeightRateValue, (void **) &old_GetCameraHeightRateValue);
   
  
   /* 
               /////////FPS CAO///////////
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);


Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported90FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
*/
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported120FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);

    // AIM SYSTEM HOOKS
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillControlIndicator") , OBFUSCATE("GetUseSkillDirection"), 1), (void *) GetUseSkillDirection, (void **) &_GetUseSkillDirection);

   Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager") , OBFUSCATE("UpdateLogic"), 1), (void *) AimUpdateLogic, (void **) &_AimUpdateLogic);

  /*
  Tools::Hook((void *) (uint64_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("Update"), 0), (void *) AUpdate, (void **) &old_Update);

   Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("LateUpdate"), 1), (void *) Skslot, (void **) &_Skslot);
Reqskill = (bool *(*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("RequestUseSkill"), 0);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("HudComponent3D") , OBFUSCATE("UpdateLogic"), 1), (void *) Hud3d, (void **) &_Hud3d);

Camera_WorldToScreenPoint = (Vector3 (*)(void *, Vector3))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("WorldToScreenPoint"), 1);
Camera_get_main = (void* (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("get_main"), 0);
ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_position"), 0);
ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objType"), 0);


Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_gameObject"), 0), (void *) Wupdate2, (void **) &_Wupdate2);
 */
  
 
   
    return NULL;
}



jobjectArray GetFeatureList(JNIEnv *env, jobject context) {
    if (!isAuthenticated() || !CheckModVersion()) return NULL;
    jobjectArray ret;

    const char *features[] = {
        OBFUSCATE("tab1_2_Toggle_Icon Mini Map"),
        OBFUSCATE("tab1_3_Toggle_Vẽ Hộp"),    
        OBFUSCATE("tab1_4_Toggle_ICon Map Lớn"),     
        OBFUSCATE("tab1_5_Toggle_Đường kẻ"),               
        OBFUSCATE("tab1_Category_Điều Chỉnh Icon Mini Map Nếu Lệch"),
        OBFUSCATE("tab1_7_SeekBar_Size Icon Mini Map_8_30"),
        OBFUSCATE("tab1_11_SeekBar_Chỉnh lệch Icon_200_650"),
        OBFUSCATE("tab1_Category_Chú ý : Thông số chuẩn : Size Icon : 19  . Lệch Icon : 410 . Cấu hình trong game để độ phân giải SIÊU CAO ."),
        
        OBFUSCATE("tab2_1_SeekBar_Cam Xa_0_50"),
        //OBFUSCATE("12_Toggle_Dump IL2CPP"),       
        OBFUSCATE("tab3_12_Toggle_Kích hoạt AIM Skill ELSU"),
        OBFUSCATE("tab3_13_RadioButton_Mục Tiêu AIM_Máu thấp nhất,%Máu thấp nhất,Khoảng cách gần nhất"),
        OBFUSCATE("tab3_14_Toggle_Bỏ qua kẻ địch tàng hình"),

        // Enhanced Aim System Configuration
        OBFUSCATE("tab3_Category_ Cài Đặt AIM Nâng Cao"),
        OBFUSCATE("tab3_15_Toggle_Sử dụng dữ liệu ESP cho AIM"),
        OBFUSCATE("tab3_16_Toggle_Dự đoán chuyển động kẻ địch"),
        OBFUSCATE("tab3_17_SeekBar_Độ mượt AIM_1_20"),
        OBFUSCATE("tab3_18_Toggle_Hiển thị thông tin khoảng cách"),
        OBFUSCATE("tab3_19_Toggle_Hiển thị đường line đến tTarget"),
        OBFUSCATE("tab3_20_SeekBar_Độ dày đường line_10_50"),

        /*
        OBFUSCATE("tab3_100_ButtonOnOff_Tự Động Dùng Bổ Trợ"),
        OBFUSCATE("tab3_101_SeekBar_Số % Máu Sẽ Dùng Bổ Trợ_20_50"),
        OBFUSCATE("tab3_102_Toggle_Tự Động Dùng Bộc Phá"),
        */
        
        // Tab 4: Tùy Chỉnh      
        OBFUSCATE("tab4_Category_Cài Đặt App"),
        OBFUSCATE("tab4_-100_CheckBox_Lưu Cài Đặt Cho Lần Sau_True")
        /*
        OBFUSCATE("9_SeekBar_Map Offset X_-100_100"),
        OBFUSCATE("10_SeekBar_Map Offset Y_-100_100"), 
       */
        
           };
    //Now you dont have to manually update the number everytime;
    int Total_Feature = (sizeof features / sizeof features[0]);
    ret = (jobjectArray)
            env->NewObjectArray(Total_Feature, env->FindClass(OBFUSCATE("java/lang/String")),
                                env->NewStringUTF(""));

    for (int i = 0; i < Total_Feature; i++)
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(features[i]));

    return (ret);
}


jobjectArray GetCategoryList(JNIEnv *env, jobject /* context */) {
    // Bỏ kiểm tra bValid
    // if (!bValid || !CheckModVersion()) return NULL;
    
    const char *categories[] = {
        OBFUSCATE("Mini Map"),
        OBFUSCATE("Camera"),
        OBFUSCATE("Khác"),
        OBFUSCATE("Tùy Chỉnh")
    };
    
    int size = sizeof(categories) / sizeof(categories[0]);
    
    jobjectArray ret = (jobjectArray)env->NewObjectArray(size, 
                                    env->FindClass(OBFUSCATE("java/lang/String")),
                                    env->NewStringUTF(""));
    
    for (int i = 0; i < size; i++) {
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(categories[i]));
    }
    
    return ret;
}



void Changes(JNIEnv *env, jclass clazz, jobject obj, jint featNum, jstring featName, jint value, jboolean boolean, jstring str) {
    if (!isAuthenticated() || !CheckModVersion()) {
        return;
    }

    switch (featNum) {
        case 1:
             Config.WideView.SetFieldOfView = (float) value * 0.0362f;
            Config.WideView.Active = true;
            break;
        
        case 2: 
            Config.ESPMenu.MinimapIcon = boolean;
            Config.ESPMenu.Enable_ESP = boolean;
            break;
        case 3:     
            Config.ESPMenu.PlayerBox = boolean;           
            break;
        case 4:    
            Config.ESPMenu.Icon = boolean;      
            break;      
        case 5:      
            Config.ESPMenu.PlayerLine = boolean;           
            break;      
         
            /*    
                case 100:
            AutoBoTro = boolean;
            break;
         case 101:
            PhanTramHP = value;
            break;
       case 102:
           AutoPhuTro = boolean;
           break;
    */
                   
        case 7:
            Config.ESPMenu.IconSize = value;
            break;           
            case 9:
            Config.ESPMenu.MapOffsetX = value - 50; // Để có thể điều chỉnh âm/dương
            break;
        case 10: 
            Config.ESPMenu.MapOffsetY = value - 50; // Để có thể điều chỉnh âm/dương
            break;
        case 11:
            Config.ESPMenu.MapSize = value;
            Config.ESPMenu.BigMapSize = value;
            break;

        case 12:
            AimElsu = boolean;
            break;

        case 13:
             switch (value) {
             case 1:
                aimType = 1;  // Máu thấp nhất
                break;
             case 2:
                aimType = 0;  // % Máu thấp nhất
                break;
             case 3:
                aimType = 2;  // Khoảng cách gần nhất
                break;
             }
             break;

        case 14:
            aimIgnoreInvisible = boolean;  // Bỏ qua kẻ địch tàng hình
            Config.FEATMenu.aimIgnoreInvisible = boolean;
            break;

        // Enhanced Aim System Configuration Cases
        case 15:
            aimUseESPData = boolean;  // Sử dụng dữ liệu ESP cho aim
            Config.FEATMenu.aimUseESPData = boolean;
            break;

        case 16:
            aimPredictMovement = boolean;  // Dự đoán chuyển động kẻ địch
            Config.FEATMenu.aimPredictMovement = boolean;
            break;

        case 17:
            aimSmoothness = (float)value / 10.0f;  // Độ mượt aim (0.1-2.0)
            if (aimSmoothness < 0.1f) aimSmoothness = 0.1f;
            if (aimSmoothness > 2.0f) aimSmoothness = 2.0f;
            Config.FEATMenu.aimSmoothness = aimSmoothness;
            break;

        case 18:
            aimShowDebugInfo = boolean;  // Hiển thị thông tin debug
            Config.FEATMenu.aimShowDebugInfo = boolean;
            break;

        case 19:
            aimShowTargetLine = boolean;  // Hiển thị đường line đến target
            Config.FEATMenu.aimShowTargetLine = boolean;
            break;

        case 20:
            aimLineThickness = (float)value / 10.0f;  // Độ dày đường line (1.0-5.0)
            if (aimLineThickness < 1.0f) aimLineThickness = 1.0f;
            if (aimLineThickness > 5.0f) aimLineThickness = 5.0f;
            Config.FEATMenu.aimLineThickness = aimLineThickness;
            break;

        case -100:
            // Auto Save Settings - handled in Java side
            // This case is just for logging/debugging
            break;

    }
}

__attribute__((constructor))
void lib_main() {
  
}


int RegisterMenu(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("Icon"), OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(Icon)},
            {OBFUSCATE("IconWebViewData"),  OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(IconWebViewData)},
            {OBFUSCATE("IsGameLibLoaded"),  OBFUSCATE("()Z"), reinterpret_cast<void *>(isGameLibLoaded)},
            {OBFUSCATE("Init"),  OBFUSCATE("(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V"), reinterpret_cast<void *>(Init)},
            {OBFUSCATE("SettingsList"),  OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(SettingsList)},
            {OBFUSCATE("GetCategoryList"), OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetCategoryList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}


int RegisterFt(JNIEnv *env) {
     if (!isAuthenticated()) return JNI_ERR;
    pthread_t ptid;
    pthread_create(&ptid, NULL, hack_thread, NULL);
    JNINativeMethod methods[] = {
         {OBFUSCATE("GetFeatureList"),  OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetFeatureList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}



int RegisterPreferences(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("Changes"), OBFUSCATE("(Landroid/content/Context;ILjava/lang/String;IZLjava/lang/String;)V"), reinterpret_cast<void *>(Changes)},
    };
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Preferences"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}

int RegisterMain(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("CheckOverlayPermission"), OBFUSCATE("(Landroid/content/Context;)V"), reinterpret_cast<void *>(CheckOverlayPermission)},
    };
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Main"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;

    return JNI_OK;
}

std::string jstringToString(JNIEnv* env, jstring jstr) {
    if (!jstr) return "";
    
    const char* strChars = env->GetStringUTFChars(jstr, NULL);
    if (!strChars) return "";
    
    std::string str(strChars);
    env->ReleaseStringUTFChars(jstr, strChars);
    return str;
}

extern "C"

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Menu_Check(JNIEnv *env, jclass clazz, jobject mContext, jstring mUserKey) {
   if(loginAttempts >= MAX_LOGIN_ATTEMPTS) {
       return env->NewStringUTF(OBFUSCATE("Quá nhiều lần đăng nhập sai. Vui lòng đóng trò chơi và mở lại để có thể đăng nhập."));
   }
   
   // Kiểm tra tham số đầu vào
   if (mUserKey == nullptr || mContext == nullptr) {
       return env->NewStringUTF(OBFUSCATE("Lỗi: Tham số đầu vào không hợp lệ"));
   }
   
   // Lấy thông tin signature và MD5 để gửi lên server
   std::string appSignature = checkSignature(env, mContext);
   std::string apkMd5 = checkBaseApkMD5(env, mContext);
   
   // Đặt flag mặc định về false
   secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
   g_Token.clear();
   g_Auth.clear();
   
   const char* userKey = nullptr;
   try {
       userKey = env->GetStringUTFChars(mUserKey, 0);
       if (!userKey) {
           return env->NewStringUTF(OBFUSCATE("Lỗi khi đọc key"));
       }
   } catch(...) {
       return env->NewStringUTF(OBFUSCATE("Lỗi ngoại lệ khi đọc key"));
   }
   
   // Lấy thông tin cơ bản về thiết bị
   jclass buildClass = nullptr;
   jclass buildVersionClass = nullptr;
   jstring jModel = nullptr;
   jstring jManufacturer = nullptr;
   jstring jRelease = nullptr;
   jstring jFingerprint = nullptr;
   const char* modelStr = nullptr;
   const char* manufacturerStr = nullptr;
   const char* releaseStr = nullptr;
   const char* fingerprintStr = nullptr;
   std::string deviceInfo = "Unknown Device";
   
   try {
       buildClass = env->FindClass(OBFUSCATE("android/os/Build"));
       if (!buildClass) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin thiết bị"));
       }
       
       buildVersionClass = env->FindClass(OBFUSCATE("android/os/Build$VERSION"));
       if (!buildVersionClass) {
           env->DeleteLocalRef(buildClass);
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin phiên bản Android"));
       }

       // Lấy tên model thiết bị
       jfieldID modelId = env->GetStaticFieldID(buildClass, OBFUSCATE("MODEL"), OBFUSCATE("Ljava/lang/String;"));
       if (modelId) {
           jModel = (jstring)env->GetStaticObjectField(buildClass, modelId);
           if (jModel) {
               modelStr = env->GetStringUTFChars(jModel, NULL);
           }
       }

       // Lấy tên nhà sản xuất
       jfieldID manufacturerId = env->GetStaticFieldID(buildClass, OBFUSCATE("MANUFACTURER"), OBFUSCATE("Ljava/lang/String;"));
       if (manufacturerId) {
           jManufacturer = (jstring)env->GetStaticObjectField(buildClass, manufacturerId);
           if (jManufacturer) {
               manufacturerStr = env->GetStringUTFChars(jManufacturer, NULL);
           }
       }

       // Lấy phiên bản Android
       jfieldID releaseId = env->GetStaticFieldID(buildVersionClass, OBFUSCATE("RELEASE"), OBFUSCATE("Ljava/lang/String;"));
       if (releaseId) {
           jRelease = (jstring)env->GetStaticObjectField(buildVersionClass, releaseId);
           if (jRelease) {
               releaseStr = env->GetStringUTFChars(jRelease, NULL);
           }
       }

       // Lấy phiên bản Build (Firmware/Software version)
       jfieldID fingerprintId = env->GetStaticFieldID(buildClass, OBFUSCATE("FINGERPRINT"), OBFUSCATE("Ljava/lang/String;"));
       if (fingerprintId) {
           jFingerprint = (jstring)env->GetStaticObjectField(buildClass, fingerprintId);
           if (jFingerprint) {
               fingerprintStr = env->GetStringUTFChars(jFingerprint, NULL);
           }
       }

       // Tạo chuỗi thông tin thiết bị đầy đủ
       char deviceInfoBuffer[1024] = {0};
       snprintf(deviceInfoBuffer, sizeof(deviceInfoBuffer) - 1, "%s %s (Android %s, Build: %s)",
               manufacturerStr ? manufacturerStr : "Unknown",
               modelStr ? modelStr : "Device",
               releaseStr ? releaseStr : "Unknown",
               fingerprintStr ? fingerprintStr : "Unknown");

       deviceInfo = std::string(deviceInfoBuffer);
   } catch (...) {
       // Ignore errors, use default device info
   }
   
   // Tạo hwid từ thông tin người dùng và thiết bị
   std::string hwid;
   std::string UUID;
   
   try {
       hwid = userKey;
       hwid += Tools::GetAndroidID(env, mContext);
   hwid += Tools::GetDeviceModel(env);
   hwid += Tools::GetDeviceBrand(env);

       // Tạo UUID từ hwid
       UUID = Tools::GetDeviceUniqueIdentifier(env, hwid.c_str());
       
       if (UUID.empty()) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           
           // Giải phóng tài nguyên
           if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
           if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
           if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
           if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
           
           env->DeleteLocalRef(buildVersionClass);
           env->DeleteLocalRef(buildClass);
           
           return env->NewStringUTF(OBFUSCATE("Không thể tạo ID thiết bị"));
       }
   } catch (...) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       
       // Giải phóng tài nguyên
       if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
       if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
       if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
       if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
       
       env->DeleteLocalRef(buildVersionClass);
       env->DeleteLocalRef(buildClass);
       
       return env->NewStringUTF(OBFUSCATE("Lỗi khi tạo ID thiết bị"));
   }
   
   // Giải phóng tài nguyên của thông tin thiết bị
   if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
   if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
   if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
   if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
   
   env->DeleteLocalRef(buildVersionClass);
   env->DeleteLocalRef(buildClass);
   
   std::string errMsg = "Lỗi kết nối server";
   std::string out;

   struct MemoryStruct chunk{};
   chunk.memory = (char *) malloc(1);
   if (!chunk.memory) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ"));
   }
   chunk.size = 0;

   // Đảm bảo cleanup memory trong mọi trường hợp
   auto cleanup_memory = [&]() {
       if (chunk.memory) {
           free(chunk.memory);
           chunk.memory = nullptr;
       }
   };

   CURL *curl = curl_easy_init();
   if (curl) {
       curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
       curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/login-app.php")).c_str());
       curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
       curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
/*
       // Thêm timeout để tránh hang
       curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
       curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 10L);
       curl_easy_setopt(curl, CURLOPT_LOW_SPEED_LIMIT, 1024L);
       curl_easy_setopt(curl, CURLOPT_LOW_SPEED_TIME, 10L);
       
 */      
       struct curl_slist *headers = NULL;
       headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
       curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

       char data[4096] = {0};
       snprintf(data, sizeof(data) - 1, 
           OBFUSCATE("game=lqm&key=%s&deviceid=%s&version=%s&gamecode=%s&device_info=%s&mod_description=%s&apk_md5=%s&signature=%s"), 
           userKey, 
           UUID.c_str(), 
           MOD_VERSION, 
           GAME_CODE, 
           deviceInfo.c_str(), 
           PhienBan, 
           apkMd5.c_str(), 
           appSignature.c_str());
       
       curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
       curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
       curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

       CURLcode res = curl_easy_perform(curl);
       if (res == CURLE_OK) {
           if (chunk.memory && chunk.size > 0) {
               // Đảm bảo chuỗi kết thúc bằng NULL
               if (chunk.memory[chunk.size - 1] != '\0') {
                   char *ptr = (char*)realloc(chunk.memory, chunk.size + 1);
                   if (ptr) {
                       chunk.memory = ptr;
                       chunk.memory[chunk.size] = '\0';
                   } else {
                       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                       free(chunk.memory);
                       curl_slist_free_all(headers);
                       curl_easy_cleanup(curl);
                       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ khi xử lý phản hồi"));
                   }
               }
               
               try {
                   json result = json::parse(chunk.memory);
                   if (result.contains("bool") && result["bool"] == "true") {
                       if (result.contains("token") && result["token"].is_string()) {
                           std::string token = result["token"].get<std::string>();
                           std::string auth = "lqm-" + std::string(userKey) + "-" + UUID + "-Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E";
                           std::string outputAuth = CalcMD5(auth);
                           
                           g_Token = token;
                           g_Auth = outputAuth;
                           
                           if(g_Token == g_Auth) {
                               if(!CheckModVersion()) {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   cleanup_memory();
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Phiên bản mod không trùng khớp với dữ liệu trên server. Vui lòng cập nhật!"));
                               }
                               
                               // Thiết lập trạng thái xác thực hợp lệ
                               secValue2 = secValue1 ^ 0x12345678;
                               
                               loginAttempts = 0;
                               out = StrEnc("T#y?xVp?a~`Jhj{>]7", "\x2F\x01\x0B\x5A\x0B\x74\x4A\x1D\x2E\x35\x42\x66\x4A\x02\x1F\x1C\x67\x15", 18).c_str();
                               
                               json responseObj;
                               responseObj["res"] = "OK";
                               
                               if (result.contains("user") && result["user"].is_string()) {
                                   responseObj["user"] = result["user"].get<std::string>();
                               }
                               
                               std::string userInfo;
                               if (result.contains("o") && result["o"].is_object() && 
                                   result["o"].contains("hsd") && result["o"]["hsd"].is_string()) {
                                   userInfo = "HSD : " + result["o"]["hsd"].get<std::string>();
                               } else {
                                   userInfo = "HSD : Unknown";
                               }
                               
                               std::string encodedInfo = base64_encode(userInfo);
                               responseObj["hd"] = encodedInfo;
                               
                               if (encodedInfo.length() >= 10) {
                                   // Chuyển đổi JSON object thành chuỗi để trả về
                                   out = responseObj.dump();
                                   RegisterFt(env);
                               } else {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   free(chunk.memory);
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Có vấn đề về KEY của bạn, vui lòng liên hệ Admin để biết thêm thông tin!"));
                               }
                           } else {
                               // Token không hợp lệ, đảm bảo xác thực không hợp lệ
                               secValue2 = secValue1;
                           }
                       } else {
                           secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
                           errMsg = "Không nhận được token từ server";
                       }
                   } else if (result.contains("mes") && result["mes"].is_string()) {
                       loginAttempts++;
                       errMsg = result["mes"].get<std::string>();
                   } else {
                       loginAttempts++;
                       errMsg = "Phản hồi không hợp lệ từ server";
                   }
               } catch (const json::parse_error& e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu: Không phải định dạng JSON hợp lệ";
               } catch (std::exception &e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu";
               } catch (...) {
                   loginAttempts++;
                   errMsg = "Lỗi không xác định khi xử lý dữ liệu";
               }
           } else {
               loginAttempts++;
               errMsg = "Không nhận được dữ liệu từ server";
           }
       } else {
           loginAttempts++;
           errMsg = curl_easy_strerror(res);
       }

       curl_slist_free_all(headers);
       curl_easy_cleanup(curl);
   } else {
       loginAttempts++;
       errMsg = "Không thể khởi tạo kết nối";
   }

   // Cleanup cuối cùng
   cleanup_memory();

   if (userKey) {
       env->ReleaseStringUTFChars(mUserKey, userKey);
   }

   return isAuthenticated() ? env->NewStringUTF(out.c_str()) : env->NewStringUTF(errMsg.c_str());
}

JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    loginAttempts = 0;
    
    // Khởi tạo giá trị bảo mật
    srand(time(NULL));
    secValue1 = 0xA5A5A5A5 ^ (rand() & 0xFFFF); // Tạo giá trị cơ sở ngẫu nhiên
    secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ lúc khởi động
    
    g_Token.clear();
    g_Auth.clear();
    
    JNIEnv *env;
    if (vm->GetEnv((void **) &env, JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    
    if (RegisterMenu(env) != 0) return JNI_ERR;
    if (RegisterPreferences(env) != 0) return JNI_ERR;
    if (RegisterMain(env) != 0) return JNI_ERR;
    
    return JNI_VERSION_1_6;
}








