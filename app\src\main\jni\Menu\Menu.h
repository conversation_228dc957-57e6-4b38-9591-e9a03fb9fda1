bool iconValid, settingsValid, initValid;

//Big letter cause crash
void setText(JNIEnv *env, jobject obj, const char* text){
    //https://stackoverflow.com/a/33627640/3763113
    //A little <PERSON><PERSON> calls here. You really really need a great knowledge if you want to play with JNI stuff
    //Html.fromHtml("");
    jclass html = (*env).FindClass(OBFUSCATE("android/text/Html"));
    jmethodID fromHtml = (*env).GetStaticMethodID(html, OBFUSCATE("fromHtml"), OBFUSCATE("(Ljava/lang/String;)Landroid/text/Spanned;"));

    //setText("");
    jclass textView = (*env).FindClass(OBFUSCATE("android/widget/TextView"));
    jmethodID setText = (*env).GetMethodID(textView, OBFUSCATE("setText"), OBFUSCATE("(Ljava/lang/CharSequence;)V"));

    //Java string
    jstring jstr = (*env).NewStringUTF(text);
    (*env).CallVoidMethod(obj, setText,  (*env).CallStaticObjectMethod(html, fromHtml, jstr));
}

jstring Icon(JNIEnv *env, jobject thiz) {
    iconValid = true;

    //Use https://www.base64encode.org/ to encode your image to base64
    return env->NewStringUTF(
            OBFUSCATE("iVBORw0KGgoAAAANSUhEUgAAAFoAAABaCAYAAAA4qEECAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAABqoSURBVHic7Z15nBXVlce/p6re2v26abpBkEYQkYawKKCoIItRMVFRQcFlEtEYY6KZyfKJcSbJJNHsk9EYHZOZLBqj0SiLazQo4sIigoACQjeNgNjdbL1vb60680e99/q93hFUFA4fwVd1695zf3XrnHvPOfce4cglY9DI2QW5g04bJP6BA/H384pGjlMrRwAk0exgBvcS3ROTpj1VzdWlVbs3PVIP6MfMd6ckHzcDLs01h587vsQqOGWqegrGYwZHq+EpEStU4OA1QTL4FDT7F6iqoZEEieYaNF4qifBmjR/YED+wdvmO1376LuB85F1qRx8b0EUlXwoVjr7oAvEXz1Ir9zzH6jsQMUQRhLZhKbT/4f6UjH87koKCaEIlUfceTssSaS1/tnntUy9VVCwIf4jd6pI+aqCNkll/nCGhcfMdT9/L1MoPoYiLSntWkuiqgHQFabs30MXjoIiihl1XS6x+odPw9gPlz920ho9QzHwkQB8/cVYwdOJXryRwwjcdT7+xiNGrdlUVAUQc/F7wexw8lqAKCVsJxwyicVA1UJHed0Ztx4ztWystO+7eWvboYrYsiH3gzvWSPlygPzPXO+Iz879McNi/q1U4GJEO37uSHLAoXsuhZLDJ6CEWJcUWQ/ubHF9kEgqaxBNKJAZx20EAj2Xg94JpCg3NNlXVCXbucyirSLB5V5ztVQ4J2+jkSwElKaBU1YgfKKe1/Cdlix55FBbYHxYUHxrQJXMev0RzRv9CPf1GISouopJG1u2qMrhI+eypPqaO8TJysIede+Js2pWgrCLBzr0OldUJGlrBdkBVMlhWRBTLgD65MKjIYthAg5HFFmNP9FDcz2LzzhivbYqx7O0Y++qEDi9a05JDjdje9TS+dWvZ0/Nf/jDwOOxAnzTzzsFWvyn/4/hOmIVY2fWr28tQ0OHiSV5mT/ETChq8tCHKq5uivL3DJpYwDwMXSsDjMPFki2njvHz2VD97ahIsWhFhybo44ajRSc8VNGGbkfcfcqr+eeu2V35cfRgYSdPhBFpGXfHkF53csb+xzVDfbP3l/hhcpMw/z8/5EwO88naEhSsibN7loGq4ZeRwswSoYhjKxOEGV0wNcMZIH0+/3srDyyLsrzeSnLdxCYqRaKiUprduLls87+nDxcZh6dXwM/41zxp21e/swLBrFFOQzA9cGVig3HJxkMmj/TyyrIXHl0dpDBsf6ZTHlVhKYZ5y9Qw/l08N8s+1Yf7wfJi65kyR5H54QsI2W7fdl3hz0Xe3b783eqjtH3Jfh138vyd7+p690LH6jWtTPIoCfku54XM+5k7L4aGlLTz6SpRwzEgV+WgnlxkzQUXJCyjXne9nzpQg97/QzKMvx0jYSTmeUpeqGPGqFcb+V+ZtXfKtPYfS/CF1dcSlj06l4LRFjpnXLzW5cvFTJpwk3HFtHuu3x7j7iVZqmiU5HT4SFqMpLuH4AofvzstlUKHJfz7YSFkFaR5TMyIjUfMe9WsuKXt6/sYP2uIH7nXJnEWXaN7ERxwzmJM5jj2GcsssP58/PcAP/9rAmm0f9dA9OHLHLnz2FJPvXx3ioaUtPLg0hmIk77tkJhprqFt3adnTV678IO18IARGXvr4NU7fMx5wJOBtk8dKUR7c+ZUQ++tsbv9bC01hIUtgH6GkKKhQlOfwi+tDROPK9x5opjGSMgeIq1SdlhajZtWc0me+8MLBtnHQEJTMWXSJ5p+2wDH83rS4UKWkGO69uQ8PLW3h4ZfjcMSIiYMhxRC4ZZaX8ycE+Nq9DVRWJ60qyb6I3dxi1L0xs/Spq1cdTM0HNWkdcemjU+kz6Qk1ggFJgahw+gjht18r4PaHGnhmrYOofAJBBlduC2tKbZrCCX51Qz5vlEaobkqCDKjh8eItvKzghIn/qN325IHe1txroE+64L7hRtHUF9QK9WmbdypTRxv8/Pp8/u13DazdnmKXI15cdE9CWZXyblWMu27KZ8P2CPvqJDl2BAx/0PT1/Vxe0fGP1e9c1tKbGnsFdL8ZP87NOf7CfzqevsOE7JH88+vz+eo99WypcO988kGGlFp5vxre2RXlrpv6sHpLhJpGSa/ikUCBFRgwscarj7BnXY/27l4BPfisn/7Z8Q+eSdJWjMKIQcpvby7g337fkAb5U0cKVXXK9soY//2VfJauj9AYSYoREdTMG3pcqChQ/c5fX+ypqh6BHjHniS9qaNwPEUNSIBfmKX/+dh9uf6iRN8s/oeK4NySACu8fgIaWOD+4Jo9nV4eJ2ZK6JXj6nlE45KxVNaULdnZXVbdAD//8r4qlcMaTSCCYch95TIff/WseT6wK8+waB037RD6llJSHZe87HF8Il0328eK6OKmvW8UwDG/etPx+OX+p27Gyy6W60V0bZsH0/3HMUKFKUh+ruxjZX2/z8LJ40l30KQY5i4TfPhmhINfgqulW0hKpiAqOWTDU23/Wz7p7ukugR87++8WOf8glom0+vAnDhc9PCnDHwy1ttoqjB2dsFf79/iZuvDDEsAHJi64LCMc/5KYRF/9pUlePdw70xK94NHfMfyFmWir4LOX2a/P44YMNNEY691wcDbS3zuC/FzZyx/wQhjhtvkl8lhSc9ku6GHqdAj3yxEu/5Hj7j3QrcP++4QIfb70bY02ZJl1PRykJPLfWpjnsMHuyJ+WHQwUcz4AZJXMen9XZYx2ALi6eG9Dg0O+lzfAKA/oo86bn8JvFre5IPjoHM+npLcIvHmvh5lkhcv2avJJ0rgVP/jGd4NrhQu7kL81zPP0Gp/1potw8K8jDS1uoaT5qEe5A7+2DF9eHue58P9Bm5VPvwFNHXvb389qXbw+0qLf4mwoi4lqsiguVKaP9PPJK9OgdyJ2RCH98PswVU4PkB520rV1FRHNO/nb74llAj7rkT1Mdb9Ep7sLEnVbMPz/AIy+3JD0jx6DOpJpGYcmbYeZN87lxPrjCRX3HnXvyeb8alVk2C2gnd9x1iplaYxIKOsycGGDBa1FUj2YN2DmpwF9fCnPl9CAew8m47rHMorO+mFk2DfRx476Yo54+czKsn1w0ycurGyM0thrIUTqd644EobJaKK+MM21sahHjYqeevlfD3PTKOw10wUmXXKBmn7y2SpTZk/0sXB45JjG6JWHhighzpvizrjmeoiEjL56eXsCkgdbgCReppEICobhQyc8x2bTrY494PbJJYMXmOOOGeQkFXKzcqbWI5I2/MFUsCfRcU42cmSm7hagbpvXShkgyuOUYdUeRhMHqrVHOHuN1L6SW5Ubu51NlDIARM8efrJ6+g9IiQmDaGC+vbvrQgyw/FSQIr22KMXWMl6w5gyc0btCkWwohCbSZP24KYqYlscd0GDnEw1s7Esfkcy9p9dYYp5d4kYzNBY7keEKDJp0FSaAdT+HETERLik127kkQixtH6I6QI4+qmwwStjKgIGOVKIL6hoyHlIw2fGMyHxoz1GLTznjPdg1NBX918TYyL6t2+9KyaumhbIf7HX63a16BTtYB2kkjmrp6sOsGhc0743xmiJV93cwZDWABhoq3pC1ISigZZPHWjniPdQf9MHaoiSqsLbeT8csZJMqE4QYeUyh936Ghtfv6xg8TfB6Xize3JXA6VcRKbhBGn+Deq2l02F7VNiBS/eifr5w2wkNe0KC+xWFtWZzqpmxfkKgw6gQhL5gd4FhZk6CqBhyVg7AGC2WVCUqKLZa9HUteAcTzGQCrePTcPmKF+mZ2ZOhxJotX9RxAOfQ44aFbC4nZymm37CPSSWzzfbf0oW/I4sa7a3ltc9cB9QLceWMeg/t5UVWuv6uGVVuzR1Xq1zUzfHzn8nwAnn2jiW//MZK8r3hN5dYrAlxzTghPBjuxhHL/C03c82QE25H01/r9q3KZVOJ3Q8M0uedFobwqxj1PNfPCepveKCoV2LnXYeYEl//UAk8N3xAmTvRYuQPHDrLFZ6ZLoxxfZFJZneAg42sOD6nrg7z8bD8rt7a280cqhiizJweSsbXZAAjws+tyuPTMHJojDn99qYVdex1OHmQwd2ouX70oj9yAwU/+Fk7bclK06p0w69+N4bMMxgz1cOYoP/d+rS93LW7g/56L9ejoEIWK6gTFRf6sesUK5g5wxvWxCAwamLTVgSiCQyho0dCrsJDDTwrYjsO544P0Cba4fGR0csJJJsMGeLAVTMl+7uzPGFx6Zi6RuMMXflXDlgpXPKgoz62J8PBt/fiXc3J5alWEje181iu2xPnTkrgrPjXCZ09t4d6bC/nm7HzWllezbnv3TmgVOFCvFOWbZO4iU3wSDBUPMNRT5NeMCbTfC7GEg/MxzjZWbmkh4BUunOTNUlcCzJniA+D1rY1Zz4jC3KkBBOWp11vYWkF6E5KosH4HLFnXggCXT/Wjoh0kQubPZW85LFzejCFwwwXBHr1KAjS32uQELIyMKZ4i4g0N9BqikeMyVXbA6xCJ0VGxfYT0/NoIqjBnSiA9mFWVHJ9ywWlBGsMOL7+dyHpGxGH8SV5UhBXvxNqCLFMzJxVWbYkjIkw4ydvN2JT0c/9c5+qpM0b6scyeTREJNTAMEM3QRW7zAyw8Idr83ILHFOIJp9fOVwUsA/7wzRBOJ7zkBQ5yCS/Czr02m3dFGTvUx4hBsK3SZXjmBA8hv8HfX21GJHsa5TGVvnnutb11nclvZX+9+3IK8wXBwelBB1VVuwMw5DcIBYW65u5Zd1RwHAfTlDYsVMBXgNVlxEGv48ddwTOpJNjx5WRoX7ekMvssDyOKrczHueepMJF4euji9ZgsWhFm7FAfcyb7+eXjEUTcEQ6wcEWEyaN8WU0ZBpjJfaLx7MGe7K/gJL9Sy3D73FP3EnbKnQde64N+4W4dliSa1FXgSSZtxWMdxChUiNtw1jf2EIm3f05ZfmcRBSGPy68K5433cf6EYFsJhT/9M0w4OW0XgRy/yT/WxLhtnjLrrCB3LQ4zoK9w2gg/pRUxNu9Spo3Jbsm2Xd0S8AiB7HeQbFvxJLVnLOHQm1Hk87o+E1UlHO155BmiGIaBbWfKf4FoHZYavr2ZWjIcc3ekijhor6Z3AqLEbYO40758auNC2++f/b2F3z7VmrWSq28hS9n4PNDYKrywvpVLz8xh2liL0UM8GAILV4RRBa+VrZ3ijrC3NsGJx3kY2t9g/fZEFjAqcEJ/90uqqnba8dU5lRS72/IONNg092KrviUOjkM73BSFvQbRA7GUhAaIxMBrGfRut/bBxtEIVbVQXulQXqWUVynbqpx2sj01LcJ1OgBXTvNx6eQA0bjy7BvRziOkVFi1xVVeMyf6SVeibZt+zhvvQ1FWl/ZslRSUedOCgLDsrTB299FzAOQGTVrCifT+l2Q9GmvaEzMIV+4RbZtjqApNrQnyc3qs9+ApvVLOmNCntXy7oiK8WW6za3+caWNzGNTX4qW3wtQ1GdDJjEgF/rYsTNyGGeMCXHG2BSipuMFrzrGYNMJPNKY89mqELsWAKkGvzfevDjB5lJ/miM0fng/3aFxToF8fobrBbjcljWprU8Veq3nPpsqcwVFbJWAle0hVtc2gIova5o/XdOc4Bk+sbOXbs/PdEb4i+f12gdG7e+CXj9fzg6v78LP5BVw1PcqOvQmGD7QYPcSHKvz00XoqarPWFABcPT3IjLE+vB4YfryHXL9BU9jhG/9bR2WNIF0eZZFkSaG4yKKi2smeACQijXs37KyzKt5ZUD9iwo9q8Qb6p3qxa5/NsAEGm3Z1v9lfk4owYXf9whOOe9/RlMTumlnbcetzVNLf+5OronxtFuyrS7B6qw1ioICTLGsnLXOpzj38Upw9NbV847Jcxgz1MXaoDwW2vh/j7ieaeGVTao9NG39xGwYWWgwstHAU9tbGWbyylQdeCFNZm4pP6l5GqsCJAwx27bMzZmyKOJH34ZWEAJR8Yf1rjq94auqhq6ZbnNDP5L8WdmdYctWJJzmRj9kd4z4U8Jnuy4rb7nqpK6AV8Jo2AiQcIz0VA8VjOqhKemeroliGYoriqBB32m13VleZF+Up+bkG9c0ONY2Cg2S3r2CZdpY+UiBuS8aQ6KUSUuXXXw7y/JtRlr3tJC8pZnT3Y2V/O/0qV2o70c2Zz2zelWDsiVan9to2cpmJJUyittmpFheFWMIkZps9HmokqsRTZTNlsAox2yTutI0qUSHhuNcTdkrxZPAq4GCwv9GgvAoONBrtjqBoYzDuuG2m/ovbZoYyOwhNLzBmqIct7yXSuAmA3fIOJFcrRrxmXSajZRU2Jw7w4OvFsjOl4DplKeOGtB9NHcp2VIjZ9Wfcl4wvoxMF29Z02x86PaGmc46kyztdU1HIwbKEvfXZU2iJvrcBkkDbDRtXitrpARxPGJTujnPqcOuYJ6uXdOYoL2vLYllfjmG3xJsq17wOSaC3vbChnHhdZeareG1zjOljvAfv0jkaSZVpY70s3xzLjuhKNG2sXHNfDaQNHQts7KalmZAueyvKueP9GEd11HnPpIDP43DmKB8rNsey7zjNz6d+pZcwRvj9ZyRDilfUCA0tNmOHGt25X496EpSzx3jYuCNGYyS59FYFVZXGDc+lyqWBrnv36SViNySt6a7z7IlVEa4424/oUb6dogvS5Pr+irP9LF4ZycBIMOLV75U+++qa1JU00Ps2PtQi8brFmmEf/ceaGNNP8ZMX7OiNOEYAwqAi5eRBXl7b1BZspCgSr8063i3LUmI0b/yLqLuGQ5IWtHVh5k7zJd/esWGdTcq15wZ47NUWErak40EM4gm7+vWHMktmAb316S8vN2LVb6cWHyLCgy+GueacHIJeRY/FSGdRUUi54LQAj7/mrqBdj5kg0X0vlS+9bWtm2Q6WeolV3C3atiisqBZWvhPhmhk+5NiIBkiP3Bs/H2Dh8lYaWt19l6qKqKPSUn5X+2c6GFmbV93/uJE48H56niHwu2da+cJ5OfQNdRsAdlTRkOPg/AkB/vJiKnjHlQAS2/NW6ZNXLW1fvgPQFRULwtK66+dtPg9hb73w+KstfGtOEDkGNYLyH1fm8vtnmmiKZNplHJWWstvp5LzqTt0GpTufut+I7y9tMyoJf14S5dSTvEwaIZ0a3o8WUpQLTzfJDQiLV7mnHIALvhmtWl72xFWdnv7YuX9m3R/i0rz5u6JOWirH4gY/+msjd8zPd7cQHIXyWlEG5CvfuSKPHz7YhKNt5lnRWMJpWH8rXXzuXXpfq0sXbus3au54PAUj3ZpgT61SGHK45Cw/L25oe5tHC1mi3HNzHgtea2X5O0lPejIayozs+H3Zosv+2NWz3Xoc7bpXv27YzTWarAwR7nsmSv8+Jl/4rAe0e4v1p4HSGkmVb1zmp67Z5u+vxtP2dwUMu25XbP8z3++unm7jCWq3L20sHDZjD/5Bl7lmKTcIZfmmCD+5No+de6PsPvDpHtcpOC87y+SyKUG+fl8TUbtNZBjEE0b9mqvLn/vWlu7q6TFwo2brYxv7jZw9XK3CcSkre2sM1pRGuPOmAtaXR9jfcOgdOlJJgSmjDG67Mo8b72pIHjyQPsRLzfC2X5cuvPgPPdXTqwBoo+iEF4O5Qy9yrBz33BUVapqEd96LcNdX8nl9S4SaRg7unP0jmDL93acPh1/ckM/N99azc3/Sa5MUpUa04pWysgduOGzHsbXueiXWZ+DopWaw+Go1fDkpt1BlLeyoinHnV/J5szzK/oZPlxiZMkr4xQ35fOP3DWzd3RY2h4Bh1+2IVT1zYf3KXzd2X4tLvQ7pr3v3udrCoZNXi2/APBXLm2r0/f2wZXeUu77ahx1VUXbv7xjJ+YkiBRFl1ukm/3GVO5K37Ja2PqkidmONWb38c+VLvrmz+8ra6KD2TtSULdpdNGzqZvH1vxxxQwZV3JG9emuYX92Qj6rNxl12xuf3CQJdFcOAr1/iZd70HL58dz079pLlnhKnpUXqVl9U+ux16w6m6g+EQtuxxn5v+rxOXE/wnTflZR1r3Ln3+cgjRSkKKT+/PkQsoXzv/ubk8jpzGtf8gY81/kC7garLFmwqOnHKJvH1n6WGx+vuE4HWmPDs6ggjB7uHXpdXRqmqaYsoPdIAz4y/OPcUg3tv7sM/***********************************/kLYOqe/uMcenLVIzr1+WXFZl4nDh9uTR8795opXaJjnyzvxQZWBf5bvzcikuMvnBXxoprYTMqA5FMeO1h3z0/CHtb6spW7Q7f/DoJy1f0XQ1g8elw6hEqKpTFq+IMKLY4I5r8zHEzfoTTzp3Pg6Bohn/lxdQbrrQzw+uyeO5tWF+9FAr+xvcwZDmTBUzvmeFsX/p50qf++q7h9L2IW8krNv2bK1R0P/hnEBRsVp54xQRl1nBtoW15TZL3gxzwQQft10ZwjIc3q1KEE1Ih4jOD400ZXB05fANF/j5yfw8du9L8N0/N7G6zEEd95SdtL1SE7bRUn6Pvebha7etvaP+UFk4nN2Uksuf/RcNjfqtY+T27Sgm3HRN888PcP6EZMKb5RE2v+d0DD48jKQKpuF0nvCmoRNTj4LYDZXStOHIS3iTSSfNvHOwVTTtHsdffCmYmcLO/UcgL9BFCqd3bTcqNV3WzafV03m+boSv+3mkAs9TKZymj/VyzvjsFE6tMcMtL6mY2FSSsoRtht972Nmz5DtHcgqnLEonJfP2G6XqunW1nfwDGNzPPe1m2hgvJcmkZBvbJSVrzExKlrlwEMU0oKDXScncLqckVnrPtqoa8X1v0rDhtk9MUrIsapdmr3tbiBsHPTIjzd6Q/iaDkmn2Ygkl2kmaPSuZZq+yszR7Thdn9SVltnwa0uxlkps48pYrCRzvJo7EEJKKp71C1OTfbVE/7sjtKnFkJA7Qbl+LpGrKnnK2xRDbjhn9NCWO7EjGyIsfmEb+6Oscq2C2mvmhD00LtidFjURdLYn6x536tQ+WP/f1T18q1M4oldxX/YMvEitnploFA1WsjJSSB3+kfZsrORlsdRQn9+2CkumqC0+dombfiZj+7HTVSe9OpjBIix3X2aSGRmwSzdVovFSd8GaJHktX3VvKSsBuBPr4HTveH08+4CCJZkcN/z6J7YtK056qhj0b9laWPlHLERp08v9mF9s1TL40hwAAAABJRU5ErkJggg=="));
}

jstring IconWebViewData(JNIEnv *env, jobject thiz) {
    iconValid = true;
    //WebView support GIF animation. Upload your image or GIF on imgur.com or other sites

    // From internet (Requires android.permission.INTERNET)
    // return env->NewStringUTF(OBFUSCATE("https://system.cheat-aov.net/icon/menu.gif"));

    // Base64 html:
    // return env->NewStringUTF("data:image/png;base64, <encoded base64 here>");

    // To disable it, return NULL. It will use normal image above:
    // return NULL

    //return env->NewStringUTF(OBFUSCATE_KEY("https://i.imgur.com/SujJ85j.gif", 'u'));
    return NULL;
}

jobjectArray SettingsList(JNIEnv *env, jobject activityObject) {
    jobjectArray ret;

    const char *features[] = {
            OBFUSCATE("Category_Cài Đặt"),
          //  OBFUSCATE("-1_Toggle_Load Setting Cũ"), //-1 is checked on Preferences.java
         //   OBFUSCATE("-3_Toggle_Thu Nhỏ Kích Thước Menu"),
          //  OBFUSCATE("-6_Button_<font color='red'>Đóng Cài Đặt</font>"),
    };

    int Total_Feature = (sizeof features /
                         sizeof features[0]); //Now you dont have to manually update the number everytime;
    ret = (jobjectArray)
            env->NewObjectArray(Total_Feature, env->FindClass(OBFUSCATE("java/lang/String")),
                                env->NewStringUTF(""));
    int i;
    for (i = 0; i < Total_Feature; i++)
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(features[i]));

    settingsValid = true;

    return (ret);
}
